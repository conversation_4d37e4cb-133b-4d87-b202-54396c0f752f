{"info": {"_postman_id": "background-check-system-2025", "name": "Background Check System", "description": "Complete API collection for WellUp Background Check & Security Profile system\n\n## 🔐 Authentication Required\nAll endpoints require Admin or SuperAdmin role with Bearer token authentication.\n\n## 🌟 Features\n- Trigger background checks via Zyla Offender Registry API\n- Review queue management for potential matches\n- Approve/reject workflow with audit logging\n- Security profile management\n- Automatic tag application\n\n## 📋 Workflow\n1. **Trigger Check** → API call to Zyla\n2. **Review Queue** → Admin reviews matches\n3. **Approve/Reject** → Updates security profile\n4. **Timeline Logging** → Audit trail created\n5. **Tag Application** → Security flags applied", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔍 Background Check Operations", "item": [{"name": "Trigger Background Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "// Store queue item ID if matches found", "if (pm.response.json().queueItemId) {", "    pm.environment.set(\"queueItemId\", pm.response.json().queueItemId);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"fullName\": \"John Test Doe\",\n    \"zipCode\": \"12345\"\n}"}, "url": {"raw": "{{baseUrl}}/api/background-checks/members/{{memberId}}/trigger", "host": ["{{baseUrl}}"], "path": ["api", "background-checks", "members", "{{memberId}}", "trigger"]}, "description": "Triggers a background check for a specific member using the Zyla Offender Registry API.\n\n**Required:**\n- Admin/SuperAdmin role\n- Valid member ID\n- Full name and zip code\n\n**Response:**\n- If no matches: Direct security profile update\n- If matches found: Creates review queue item"}}, {"name": "Get Review Queue", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has items array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('items');", "    pm.expect(jsonData.items).to.be.an('array');", "});", "", "// Store first queue item ID for testing", "if (pm.response.json().items.length > 0) {", "    pm.environment.set(\"queueItemId\", pm.response.json().items[0].id);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/background-checks/queue?page=1&per=10", "host": ["{{baseUrl}}"], "path": ["api", "background-checks", "queue"], "query": [{"key": "page", "value": "1"}, {"key": "per", "value": "10"}]}, "description": "Retrieves paginated list of pending background check reviews.\n\n**Features:**\n- Pagination support\n- Only shows pending items\n- Includes member and triggering user details\n- Shows match count and risk levels"}}, {"name": "Get Queue Item Details", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has queue item details\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('member');", "    pm.expect(jsonData).to.have.property('rawResponse');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/background-checks/queue/{{queueItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "background-checks", "queue", "{{queueItemId}}"]}, "description": "Gets detailed information about a specific queue item including raw API response data.\n\n**Includes:**\n- Full member details\n- Complete Zyla API response\n- Match analysis\n- Risk level assessment"}}], "description": "Core background check operations for triggering checks and managing the review process."}, {"name": "✅ Review & Approval", "item": [{"name": "Approve Background Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Background check approved successfully\", function () {", "    // Check that we got a successful response", "    pm.expect(pm.response.code).to.equal(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"notes\": \"Verified identity and reviewed background check results. Approved for security clearance.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/background-checks/queue/{{queueItemId}}/approve", "host": ["{{baseUrl}}"], "path": ["api", "background-checks", "queue", "{{queueItemId}}", "approve"]}, "description": "Approves a background check from the review queue.\n\n**Actions Performed:**\n- Updates queue item status to 'approved'\n- Adds background check to member's security profile\n- Creates timeline audit entry\n- Applies security tags based on risk level\n- Records admin notes"}}, {"name": "Reject Background Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Background check rejected successfully\", function () {", "    pm.expect(pm.response.code).to.equal(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{adminT<PERSON>}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"notes\": \"False positive - verified this is not the same person. Clearing background check.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/background-checks/queue/{{queueItemId}}/reject", "host": ["{{baseUrl}}"], "path": ["api", "background-checks", "queue", "{{queueItemId}}", "reject"]}, "description": "Rejects a background check from the review queue.\n\n**Actions Performed:**\n- Updates queue item status to 'rejected'\n- Adds 'clear' background check to security profile\n- Creates hidden timeline entry\n- Records admin notes\n- No security tags applied"}}], "description": "Admin review and approval workflow for background check matches."}, {"name": "🖥️ Admin UI Routes", "item": [{"name": "Background Check Queue Dashboard", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "vapor_session={{session<PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/background-checks/queue", "host": ["{{baseUrl}}"], "path": ["admin", "background-checks", "queue"]}, "description": "Admin web interface for reviewing background check queue.\n\n**Features:**\n- Visual queue management\n- One-click approve/reject\n- Real-time status updates\n- Risk level indicators\n- Member details display"}}, {"name": "Admin Dashboard", "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "vapor_session={{session<PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/dashboard", "host": ["{{baseUrl}}"], "path": ["admin", "dashboard"]}, "description": "Main admin dashboard with Background Check Queue access card."}}], "description": "Web-based admin interfaces for background check management."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set timestamp for requests", "pm.environment.set('timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test for authentication", "pm.test(\"Authentication check\", function () {", "    if (pm.response.code === 401) {", "        pm.expect.fail(\"Authentication failed - check your admin token\");", "    }", "    if (pm.response.code === 403) {", "        pm.expect.fail(\"Access denied - admin role required\");", "    }", "});"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "adminToken", "value": "your_admin_bearer_token_here", "type": "string"}, {"key": "memberId", "value": "member-uuid-here", "type": "string"}, {"key": "queueItemId", "value": "", "type": "string"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "your_session_cookie_here", "type": "string"}]}