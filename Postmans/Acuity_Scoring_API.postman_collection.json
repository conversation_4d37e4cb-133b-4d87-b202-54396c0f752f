{"info": {"_postman_id": "acuity-scoring-api", "name": "Acuity Scoring API", "description": "API collection for the Acuity Scoring feature - NCQA compliant member risk assessment with source references and timeline logging.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"{{username}}\",\n  \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}]}, {"name": "Acuity Scoring", "item": [{"name": "Create Acuity Score", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"score\": 7,\n  \"level\": \"High\",\n  \"source\": \"AssessmentEngine\",\n  \"notes\": \"Multiple indicators from recent assessments\",\n  \"references\": [\n    {\n      \"type\": \"assessment\",\n      \"ref_id\": \"{{assessment_id}}\",\n      \"title\": \"PHQ-9\",\n      \"reason\": \"PHQ-9 score of 18 indicates severe depression\"\n    },\n    {\n      \"type\": \"hospitalization\",\n      \"ref_id\": \"{{hospitalization_id}}\",\n      \"title\": \"ER Visit\",\n      \"reason\": \"3 ER visits in 60 days\"\n    },\n    {\n      \"type\": \"note\",\n      \"ref_id\": \"{{note_id}}\",\n      \"title\": \"Clinical Note\",\n      \"reason\": \"Provider noted increased risk factors\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/members/{{member_id}}/acuity", "host": ["{{base_url}}"], "path": ["api", "members", "{{member_id}}", "acuity"]}}, "response": []}, {"name": "Create Acuity Score - Manual Entry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"score\": 3,\n  \"level\": \"Low\",\n  \"source\": \"Manual\",\n  \"notes\": \"Member showing improvement, stable condition\",\n  \"references\": [\n    {\n      \"type\": \"careplan\",\n      \"ref_id\": \"{{careplan_id}}\",\n      \"title\": \"Diabetes Management Plan\",\n      \"reason\": \"Following care plan successfully\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/members/{{member_id}}/acuity", "host": ["{{base_url}}"], "path": ["api", "members", "{{member_id}}", "acuity"]}}, "response": []}, {"name": "Get Latest Acuity Score", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/members/{{member_id}}/acuity", "host": ["{{base_url}}"], "path": ["api", "members", "{{member_id}}", "acuity"]}}, "response": []}, {"name": "Get Acuity History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/members/{{member_id}}/acuity/history", "host": ["{{base_url}}"], "path": ["api", "members", "{{member_id}}", "acuity", "history"]}}, "response": []}]}, {"name": "Erro<PERSON>", "item": [{"name": "Invalid Member ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/members/invalid-uuid/acuity", "host": ["{{base_url}}"], "path": ["api", "members", "invalid-uuid", "acuity"]}}, "response": []}, {"name": "Invalid Level", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"score\": 5,\n  \"level\": \"Invalid\",\n  \"source\": \"Manual\",\n  \"notes\": \"This should fail\",\n  \"references\": []\n}"}, "url": {"raw": "{{base_url}}/api/members/{{member_id}}/acuity", "host": ["{{base_url}}"], "path": ["api", "members", "{{member_id}}", "acuity"]}}, "response": []}, {"name": "No Acuity Score Found", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/members/{{member_without_acuity}}/acuity", "host": ["{{base_url}}"], "path": ["api", "members", "{{member_without_acuity}}", "acuity"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-generate UUIDs for testing", "if (!pm.environment.get('assessment_id')) {", "    pm.environment.set('assessment_id', pm.variables.replaceIn('{{$guid}}'));", "}", "if (!pm.environment.get('hospitalization_id')) {", "    pm.environment.set('hospitalization_id', pm.variables.replaceIn('{{$guid}}'));", "}", "if (!pm.environment.get('note_id')) {", "    pm.environment.set('note_id', pm.variables.replaceIn('{{$guid}}'));", "}", "if (!pm.environment.get('careplan_id')) {", "    pm.environment.set('careplan_id', pm.variables.replaceIn('{{$guid}}'));", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-extract auth token from login response", "if (pm.response.json() && pm.response.json().token) {", "    pm.environment.set('auth_token', pm.response.json().token);", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "member_id", "value": "your-member-uuid-here", "type": "string"}, {"key": "member_without_acuity", "value": "member-uuid-without-acuity", "type": "string"}, {"key": "username", "value": "your-username", "type": "string"}, {"key": "password", "value": "your-password", "type": "string"}]}