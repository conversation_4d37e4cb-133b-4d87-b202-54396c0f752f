{"info": {"_postman_id": "org-program-setup-collection", "name": "Organization Program Setup API", "description": "Complete API collection for Organization Program Setup service with CRUD operations for managing organization-level programs.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create Program", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "description": "Add your auth token here"}], "body": {"mode": "raw", "raw": "{\n  \"programName\": \"Long-Term Services and Supports\",\n  \"programKey\": \"ltss\",\n  \"programConfig\": {\n    \"orgId\": \"{{org_id}}\",\n    \"templateId\": \"ltss_template_001\",\n    \"programType\": \"LTSS\",\n    \"displayName\": \"Long-Term Services and Supports\",\n    \"description\": \"This program supports individuals with long-term care needs by organizing periodic assessments and program reviews to ensure ongoing eligibility, appropriate interventions, and comprehensive care planning.\",\n    \"reviewFrequencyDays\": 90,\n    \"daysToCompleteAssessment\": 30,\n    \"requiredAssessments\": [\n      {\n        \"itemKey\": \"adl_iadl\",\n        \"title\": \"ADL/IADL Functional Assessment\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"sdoh\",\n        \"title\": \"Social Determinants of Health (SDOH)\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"behavioral_screening\",\n        \"title\": \"Behavioral Health Screening\",\n        \"required\": false\n      }\n    ],\n    \"programReviews\": [\n      {\n        \"itemKey\": \"program_review\",\n        \"title\": \"Quarterly Program Review\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"barriers_review\",\n        \"title\": \"Barriers and Supports Review\",\n        \"required\": false\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{base_url}}/orgs/{{org_id}}/programs", "host": ["{{base_url}}"], "path": ["orgs", "{{org_id}}", "programs"]}, "description": "Creates a new program for the specified organization. The program key must be unique within the organization."}, "response": []}, {"name": "Get All Programs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "description": "Add your auth token here"}], "url": {"raw": "{{base_url}}/orgs/{{org_id}}/programs", "host": ["{{base_url}}"], "path": ["orgs", "{{org_id}}", "programs"]}, "description": "Retrieves all programs for the specified organization, sorted by creation date (newest first)."}, "response": []}, {"name": "Get Single Program", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "description": "Add your auth token here"}], "url": {"raw": "{{base_url}}/orgs/{{org_id}}/programs/{{program_key}}", "host": ["{{base_url}}"], "path": ["orgs", "{{org_id}}", "programs", "{{program_key}}"]}, "description": "Retrieves a specific program by organization ID and program key."}, "response": []}, {"name": "Update Program", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "description": "Add your auth token here"}], "body": {"mode": "raw", "raw": "{\n  \"programName\": \"Updated Long-Term Services and Supports\",\n  \"programConfig\": {\n    \"orgId\": \"{{org_id}}\",\n    \"templateId\": \"ltss_template_002\",\n    \"programType\": \"LTSS\",\n    \"displayName\": \"Enhanced Long-Term Services and Supports\",\n    \"description\": \"Updated program description with enhanced features for long-term care needs management.\",\n    \"reviewFrequencyDays\": 60,\n    \"daysToCompleteAssessment\": 21,\n    \"requiredAssessments\": [\n      {\n        \"itemKey\": \"adl_iadl\",\n        \"title\": \"ADL/IADL Functional Assessment\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"sdoh\",\n        \"title\": \"Social Determinants of Health (SDOH)\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"behavioral_screening\",\n        \"title\": \"Behavioral Health Screening\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"cognitive_assessment\",\n        \"title\": \"Cognitive Assessment\",\n        \"required\": false\n      }\n    ],\n    \"programReviews\": [\n      {\n        \"itemKey\": \"program_review\",\n        \"title\": \"Bi-Monthly Program Review\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"barriers_review\",\n        \"title\": \"Barriers and Supports Review\",\n        \"required\": true\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{base_url}}/orgs/{{org_id}}/programs/{{program_key}}", "host": ["{{base_url}}"], "path": ["orgs", "{{org_id}}", "programs", "{{program_key}}"]}, "description": "Updates an existing program. You can update the program name and/or the entire program configuration."}, "response": []}, {"name": "Delete Program", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "description": "Add your auth token here"}], "url": {"raw": "{{base_url}}/orgs/{{org_id}}/programs/{{program_key}}", "host": ["{{base_url}}"], "path": ["orgs", "{{org_id}}", "programs", "{{program_key}}"]}, "description": "Deletes a program from the organization. This action cannot be undone."}, "response": []}, {"name": "Create Mental Health Program", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "description": "Add your auth token here"}], "body": {"mode": "raw", "raw": "{\n  \"programName\": \"Mental Health Support Program\",\n  \"programKey\": \"mental_health\",\n  \"programConfig\": {\n    \"orgId\": \"{{org_id}}\",\n    \"templateId\": \"mh_template_001\",\n    \"programType\": \"MENTAL_HEALTH\",\n    \"displayName\": \"Mental Health Support Program\",\n    \"description\": \"Comprehensive mental health support program providing assessment, intervention, and ongoing care coordination for individuals with mental health needs.\",\n    \"reviewFrequencyDays\": 30,\n    \"daysToCompleteAssessment\": 14,\n    \"requiredAssessments\": [\n      {\n        \"itemKey\": \"phq9\",\n        \"title\": \"PHQ-9 Depression Screening\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"gad7\",\n        \"title\": \"GAD-7 Anxiety Screening\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"substance_use\",\n        \"title\": \"Substance Use Screening\",\n        \"required\": false\n      }\n    ],\n    \"programReviews\": [\n      {\n        \"itemKey\": \"monthly_review\",\n        \"title\": \"Monthly Mental Health Review\",\n        \"required\": true\n      },\n      {\n        \"itemKey\": \"crisis_plan_review\",\n        \"title\": \"Crisis Plan Review\",\n        \"required\": true\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{base_url}}/orgs/{{org_id}}/programs", "host": ["{{base_url}}"], "path": ["orgs", "{{org_id}}", "programs"]}, "description": "Example of creating a different type of program - Mental Health Support Program with different assessment requirements."}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default values if not already set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'http://localhost:8080');", "}", "", "if (!pm.environment.get('org_id')) {", "    pm.environment.set('org_id', 'org_roseman_genesis');", "}", "", "if (!pm.environment.get('program_key')) {", "    pm.environment.set('program_key', 'ltss');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test scripts for all requests", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('Response has correct content type', function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});", "", "// Store program data for subsequent requests", "if (pm.response.code === 200 || pm.response.code === 201) {", "    const responseJson = pm.response.json();", "    if (responseJson.programKey) {", "        pm.environment.set('program_key', responseJson.programKey);", "    }", "    if (responseJson.id) {", "        pm.environment.set('program_id', responseJson.id);", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "description": "Base URL for the API"}, {"key": "org_id", "value": "org_roseman_genesis", "description": "Organization ID for testing"}, {"key": "program_key", "value": "ltss", "description": "Program key for testing"}, {"key": "auth_token", "value": "", "description": "Authentication token - set this in your environment"}]}