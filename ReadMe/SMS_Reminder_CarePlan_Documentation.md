# SMS Reminder System for CarePlanService

## Overview
Successfully implemented SMS reminder scheduling for CarePlanService appointments that includes appointment dates and organization names (cboName). The system automatically schedules reminders when care plan services are created or updated with appointment dates.

## Features Implemented

### 1. SMS Reminder Scheduling
- **24-hour reminder**: Sent 24 hours before the appointment
- **2-hour reminder**: Sent 2 hours before the appointment
- **Automatic scheduling**: Triggered when CarePlanService is created or updated with an appointment date
- **Smart cancellation**: Existing reminders are cancelled when appointments are updated or deleted

### 2. Message Content
The SMS messages include:
- Member's full name
- Organization name (cboName)
- Staff member name
- Appointment date and time
- Professional reminder text

**Example Message:**
```
Hi <PERSON>! Reminder: You have a care plan service appointment tomorrow with <PERSON> at Community Health Center on Jan 15, 2025 at 2:00 PM. Please contact us if you need to reschedule.
```

### 3. Integration Points

#### CarePlanService Creation
When a new CarePlanService is created with an appointment date:
```swift
// In createCarePlanService method
if let appointmentDate = service.appointmentDate {
    try await scheduleCarePlanServiceSMSReminders(
        req: req,
        service: service,
        carePlan: carePlan,
        appointmentDate: appointmentDate
    )
}
```

#### CarePlanService Updates
When a CarePlanService is updated:
- Cancels existing reminders for both 24h and 2h types
- Schedules new reminders if appointment date is provided

#### CarePlanService Deletion
When a CarePlanService is deleted:
- Automatically cancels all associated SMS reminders

### 4. Technical Implementation

#### New Reminder Types Added
```swift
enum ReminderType: String, Codable {
    // ... existing types
    case carePlanService24h = "care_plan_service_24h"
    case carePlanService2h = "care_plan_service_2h"
}
```

#### New SMS Scheduler Method
```swift
func scheduleCarePlanServiceReminder(
    req: Request,
    carePlanServiceId: UUID,
    phoneNumber: String,
    appointmentDate: Date,
    memberName: String,
    cboName: String,
    staffName: String
) -> EventLoopFuture<Void>
```

#### Message Creation Method
```swift
private func createCarePlanServiceReminderMessage(
    memberName: String,
    cboName: String,
    staffName: String,
    appointmentDate: Date,
    hoursUntil: Int
) -> String
```

## Usage Examples

### 1. Creating a CarePlanService with SMS Reminders
```bash
curl -X POST "http://localhost:8080/careplans/{carePlanID}/services" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "cboName": "Community Health Center",
    "staffName": "Dr. Sarah Johnson",
    "addedBy": "Navigator Smith",
    "status": "booked",
    "appointmentDate": "2025-01-15T14:00:00Z"
  }'
```

### 2. Updating an Appointment (Reschedules Reminders)
```bash
curl -X PUT "http://localhost:8080/careplans/{carePlanID}/services/{serviceID}" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "cboName": "Community Health Center",
    "staffName": "Dr. Sarah Johnson",
    "addedBy": "Navigator Smith",
    "status": "booked",
    "appointmentDate": "2025-01-16T10:00:00Z"
  }'
```

## Requirements for SMS Reminders

### 1. Member Phone Number
- Member must have a phone number with label "main"
- Phone number must be in SMS-compatible format
- Uses `member.smsPhone()` method to get the correct number

### 2. Appointment Date
- `appointmentDate` field must be set on the CarePlanService
- Date must be in the future (past appointments don't get reminders)

### 3. Required Fields
- `cboName`: Organization name where appointment is scheduled
- `staffName`: Name of the staff member for the appointment
- `appointmentDate`: Date and time of the appointment

## Error Handling

### 1. Missing Phone Number
```
No SMS phone number available for member {memberID} - skipping SMS reminders
```

### 2. Past Appointment Dates
- Reminders are not scheduled for appointments in the past
- System logs but doesn't fail the operation

### 3. SMS Service Failures
- SMS reminder scheduling failures don't prevent CarePlanService creation/updates
- Errors are logged for monitoring and debugging

## Monitoring and Logging

### 1. Success Logging
```
Successfully scheduled SMS reminders for care plan service {serviceID}
```

### 2. Error Logging
```
Failed to schedule SMS reminders for care plan service {serviceID}: {error}
Failed to cancel SMS reminders for care plan service {serviceID}: {error}
```

## Testing the System

### 1. Prerequisites
- Member with valid SMS phone number
- CarePlan associated with the member
- Valid authentication token

### 2. Test Scenarios

#### Create Service with Future Appointment
```bash
# Should schedule 24h and 2h reminders
POST /careplans/{id}/services
{
  "appointmentDate": "2025-01-20T14:00:00Z",
  "cboName": "Test Clinic",
  "staffName": "Dr. Test"
}
```

#### Update Appointment Date
```bash
# Should cancel old reminders and schedule new ones
PUT /careplans/{id}/services/{serviceId}
{
  "appointmentDate": "2025-01-21T10:00:00Z"
}
```

#### Delete Service
```bash
# Should cancel all reminders
DELETE /careplans/{id}/services/{serviceId}
```

## Integration with Existing Systems

### 1. Twilio SMS Service
- Uses existing TwilioController for SMS delivery
- Leverages existing SMS authentication and configuration

### 2. AWS SQS Queue
- Reminders are queued using existing AWS SQS integration
- Background processing handles actual SMS sending

### 3. Database
- Uses existing SMS reminder tables and models
- No additional database changes required

## Future Enhancements

### 1. Configurable Reminder Times
- Allow organizations to customize reminder timing (e.g., 48h, 4h, 1h)
- Store preferences in organization meta data

### 2. Reminder Preferences
- Allow members to opt-out of SMS reminders
- Support for email reminders as alternative

### 3. Appointment Confirmations
- Two-way SMS for appointment confirmations
- Automatic rescheduling requests

## Conclusion

The SMS reminder system for CarePlanService is now fully integrated and operational. It provides automated, professional SMS reminders for care plan appointments, improving member engagement and reducing no-shows. The system is robust, handles errors gracefully, and integrates seamlessly with existing infrastructure.
