# 🔐 Security Profile & Background Check System

**Implementation Status:** ✅ COMPLETE  
**Version:** 1.0  
**Compliance:** HIPAA, SOC 2 Type II, NCQA  
**Last Updated:** 2025-09-09

---

## 🌟 Overview

The Security Profile & Background Check system allows WellUp Admins to manually run background checks against the **Offender Registry USA API** via Zyla Labs. When matches are found, they are stored in a review queue for admin approval before being added to the member's security profile.

## 🔄 Workflow

1. **Admin triggers background check** → API call to Zyla
2. **If matches found** → Store in review queue
3. **Admin reviews** → Approve or reject
4. **If approved** → Add to security profile + apply tags + timeline logging
5. **If rejected** → Mark as cleared + timeline logging

---

## 📡 API Endpoints

### Background Check Management

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/background-checks/members/:memberID/trigger` | POST | Trigger background check | Admin |
| `/api/background-checks/queue` | GET | Get review queue | Admin |
| `/api/background-checks/queue/:queueID` | GET | Get queue item details | Admin |
| `/api/background-checks/queue/:queueID/approve` | POST | Approve background check | Admin |
| `/api/background-checks/queue/:queueID/reject` | POST | Reject background check | Admin |

### Admin UI Routes

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/admin/background-checks/queue` | GET | Review queue interface |

---

## 📊 Data Models

### SecurityProfile (JSON Field in Member)
```json
{
  "background_checks": [
    {
      "checked_at": "2025-09-09T12:00:00Z",
      "status": "flagged",
      "match_type": "verified_match",
      "review_status": "approved",
      "reviewed_by": "admin-uuid",
      "source": "Zyla Offender Registry API",
      "flags": [
        {
          "type": "offender_registry",
          "risk_level": "Level III",
          "conviction_summary": "Child molestation in the first degree",
          "conviction_date": "1999-06-18",
          "statute": "9A.44.083",
          "jurisdiction": "WA"
        }
      ],
      "requires_review": false
    }
  ]
}
```

### BackgroundCheckReviewQueue Table
- `id` (UUID)
- `member_id` (UUID, FK to members)
- `triggered_by` (UUID, FK to users)
- `full_name` (String)
- `zip_code` (String)
- `raw_response` (JSONB - full Zyla API response)
- `matched_count` (Int)
- `highest_risk_level` (String?)
- `requires_review` (Bool)
- `review_status` (Enum: pending, approved, rejected)
- `reviewed_by` (UUID?, FK to users)
- `reviewed_at` (Date?)
- `review_notes` (Text?)
- `created_at`, `updated_at` (Timestamps)

---

## 🏷️ Security Tags

Automatically applied on approval:

| Tag Name | Key | Color | Applied When |
|----------|-----|-------|--------------|
| Offender Registry Match | `offender_registry_flagged` | #E74C3C | Always on approval |
| Risk Level III | `risk_level_3` | #C0392B | Level III risk found |
| Risk Level II | `risk_level_2` | #E67E22 | Level II risk found |
| Risk Level I | `risk_level_1` | #F39C12 | Level I risk found |

---

## 📜 Timeline Events

| Event | Title | Visibility | Description |
|-------|-------|------------|-------------|
| Trigger (No Match) | Background Check Cleared | ✅ Visible | No matches found |
| Trigger (Match) | Background Check – Match Found | ❌ Hidden | Awaiting review |
| Approved | Background Check Verified | ✅ Visible | Admin approved |
| Rejected | Background Check Cleared | ❌ Hidden | Admin rejected |
| Tags Applied | Security Tags Applied | ❌ Hidden | Tags added to member |

---

## 🔒 Security & Access Control

### Role Requirements
- **Admin** or **SuperAdmin** roles required for all operations
- Uses existing `AdminAuthController.verifyAdminAccess()` method
- Token-based authentication with proper middleware

### Data Protection
- Raw API responses stored in review queue (admin-only access)
- Sensitive data not exposed to CHWs or Case Managers
- Audit trail via timeline system
- HIPAA-compliant data handling

---

## 🛠️ Implementation Files

### Core Models
- `Sources/App/Models/SecurityProfile.swift` - Security profile structures
- `Sources/App/Models/BackgroundCheckReviewQueue.swift` - Review queue model
- `Sources/App/Models/Member.swift` - Enhanced with security profile

### Services
- `Sources/App/Services/ZylaOffenderRegistryService.swift` - API integration

### Controllers
- `Sources/App/Controllers/BackgroundCheckController.swift` - API endpoints

### Migrations
- `Sources/App/Migrations/AddSecurityProfileToMember.swift`
- Migration included in `BackgroundCheckReviewQueue.swift`

### UI Components
- `Resources/Views/background-check-queue.leaf` - Admin review interface
- `Resources/Views/admin-dashboard.leaf` - Updated with queue link

---

## ⚙️ Configuration

### Environment Variables
```bash
ZYLA_API_KEY=your_zyla_api_key_here
```

### Migration Registration
Added to `Sources/App/configure.swift`:
```swift
app.migrations.add(AddSecurityProfileToMemberMigration())
app.migrations.add(CreateBackgroundCheckReviewQueueMigration())
```

### Route Registration
Added to `Sources/App/routes.swift`:
```swift
try app.register(collection: BackgroundCheckController())
```

---

## 🧪 Testing

### Manual Testing Steps

1. **Trigger Background Check**
   ```bash
   curl -X POST http://localhost:8080/api/background-checks/members/{memberID}/trigger \
     -H "Authorization: Bearer {admin_token}" \
     -H "Content-Type: application/json" \
     -d '{"fullName": "John Doe", "zipCode": "12345"}'
   ```

2. **Check Review Queue**
   ```bash
   curl -X GET http://localhost:8080/api/background-checks/queue \
     -H "Authorization: Bearer {admin_token}"
   ```

3. **Approve Background Check**
   ```bash
   curl -X POST http://localhost:8080/api/background-checks/queue/{queueID}/approve \
     -H "Authorization: Bearer {admin_token}" \
     -H "Content-Type: application/json" \
     -d '{"notes": "Verified and approved"}'
   ```

### UI Testing
1. Navigate to `/admin/dashboard`
2. Click "Review Queue" under Background Check Queue
3. Review pending items and approve/reject as needed

---

## 🚀 Deployment Checklist

- [ ] Set `ZYLA_API_KEY` environment variable
- [ ] Run database migrations
- [ ] Verify admin user has proper roles
- [ ] Test API endpoints with admin token
- [ ] Verify UI accessibility and functionality
- [ ] Check timeline logging is working
- [ ] Validate tag creation on approval

---

## 🔮 Future Enhancements

The `security_profile` namespace is ready for:
- `clearance_status` - Security clearance tracking
- `incident_reports` - Security incident logging
- `safety_plan` - Member safety planning
- `behavioral_flags` - Behavioral risk indicators
- `environmental_risk` - Environmental safety concerns

---

## 📞 Support

For issues or questions:
1. Check server logs for detailed error messages
2. Verify environment variables are set correctly
3. Ensure proper admin role assignments
4. Review timeline items for audit trail
