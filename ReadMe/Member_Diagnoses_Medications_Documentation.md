# Member Diagnoses & Medications API Documentation

## Overview
This documentation covers the new Diagnoses and Medications models that are directly associated with Members, providing comprehensive healthcare tracking capabilities.

## Models

### Diagnosis Model
**Schema**: `diagnoses`
**Purpose**: Track member diagnoses/problems directly associated with members (not care plans)

#### Fields:
- `id`: UUID (Primary Key)
- `member_id`: UUID (Foreign Key to members table)
- `icdCode`: String? (ICD-10 code)
- `description`: String (Required - diagnosis description)
- `clinicalNote`: String? (Clinical notes)
- `status`: String (Required - active | resolved | inactive)
- `dateIdentified`: Date (Required - when diagnosis was identified)
- `source`: String (Required - EHR import | self-reported | care team)
- `confirmedBy`: String? (Who confirmed the diagnosis)
- `createdAt`: Date (Auto-generated)
- `updatedAt`: Date (Auto-generated)

#### Example JSON:
```json
{
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP remains elevated despite medication adjustment.",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. <PERSON>, MD"
}
```

### Medication Model
**Schema**: `medications`
**Purpose**: Track member medications with comprehensive details

#### Fields:
- `id`: UUID (Primary Key)
- `member_id`: UUID (Foreign Key to members table)
- `medicationName`: String (Required - medication name and strength)
- `rxNormCode`: String? (RxNorm code for standardization)
- `dosage`: String (Required - dosage amount)
- `route`: String (Required - administration route)
- `frequency`: String (Required - how often taken)
- `startDate`: Date (Required - when medication started)
- `endDate`: Date? (When medication ended/discontinued)
- `prescribedBy`: String (Required - prescribing provider)
- `status`: String (Required - active | discontinued)
- `adherenceNotes`: String? (Notes about patient adherence)
- `source`: String (Required - EHR import | self-reported | care team)
- `medicationType`: String (Required - prescribed | OTC | supplement)
- `createdAt`: Date (Auto-generated)
- `updatedAt`: Date (Auto-generated)

#### Example JSON:
```json
{
  "medicationName": "Lisinopril 10mg",
  "rxNormCode": "197361",
  "dosage": "10mg",
  "route": "oral",
  "frequency": "daily",
  "startDate": "2023-05-20T00:00:00Z",
  "prescribedBy": "Dr. Smith",
  "status": "active",
  "adherenceNotes": "Member reports 90% adherence",
  "source": "EHR import",
  "medicationType": "prescribed"
}
```

## API Endpoints

### Diagnoses Controller

#### Base Routes:
- `POST /api/members/{memberID}/diagnoses` - Create diagnosis
- `GET /api/members/{memberID}/diagnoses` - List member diagnoses
- `GET /api/diagnoses/{diagnosisID}` - Get specific diagnosis
- `PUT /api/diagnoses/{diagnosisID}` - Update diagnosis
- `DELETE /api/diagnoses/{diagnosisID}` - Delete diagnosis

#### Features:
- Diagnoses sorted by `dateIdentified` (most recent first)
- Full CRUD operations
- Direct member association

### Medications Controller

#### Base Routes:
- `POST /api/members/{memberID}/medications` - Create medication
- `GET /api/members/{memberID}/medications` - List member medications
- `GET /api/medications/{medicationID}` - Get specific medication
- `PUT /api/medications/{medicationID}` - Update medication
- `DELETE /api/medications/{medicationID}` - Delete medication

#### Additional Routes:
- `GET /api/members/{memberID}/medications/active` - Get active medications only
- `GET /api/members/{memberID}/medications/by-type?type={type}` - Filter by medication type
- `POST /api/members/{memberID}/medications/{medicationID}/discontinue` - Discontinue medication

#### Features:
- Medications sorted by `startDate` (most recent first)
- Active medication filtering
- Medication type filtering (prescribed, OTC, supplement)
- Discontinuation workflow (sets status to "discontinued" and endDate to current date)

## Database Migrations

### CreateDiagnosis Migration:
```swift
struct CreateDiagnosis: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("diagnoses")
            .id()
            .field("member_id", .uuid, .required, .references("members", "id", onDelete: .cascade))
            .field("icd_code", .string)
            .field("description", .string, .required)
            .field("clinical_note", .string)
            .field("status", .string, .required, .sql(.default("active")))
            .field("date_identified", .date, .required)
            .field("source", .string, .required)
            .field("confirmed_by", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
}
```

### CreateMedication Migration:
```swift
struct CreateMedication: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("medications")
            .id()
            .field("member_id", .uuid, .required, .references("members", "id", onDelete: .cascade))
            .field("medication_name", .string, .required)
            .field("rx_norm_code", .string)
            .field("dosage", .string, .required)
            .field("route", .string, .required)
            .field("frequency", .string, .required)
            .field("start_date", .date, .required)
            .field("end_date", .date)
            .field("prescribed_by", .string, .required)
            .field("status", .string, .required, .sql(.default("active")))
            .field("adherence_notes", .string)
            .field("source", .string, .required)
            .field("medication_type", .string, .required, .sql(.default("prescribed")))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
}
```

## Member Model Updates

The Member model has been updated to include relationships to both new models:

```swift
@Children(for: \.$member)
var diagnoses: [Diagnosis]

@Children(for: \.$member)
var medications: [Medication]
```

## Status Values

### Diagnosis Status:
- `active` - Diagnosis is currently active
- `resolved` - Diagnosis has been resolved
- `inactive` - Diagnosis is not currently active but may recur

### Medication Status:
- `active` - Medication is currently being taken
- `discontinued` - Medication has been stopped

### Source Values (Both Models):
- `EHR import` - Data imported from Electronic Health Record
- `self-reported` - Information provided by the patient
- `care team` - Information added by care team members

### Medication Types:
- `prescribed` - Prescription medication
- `OTC` - Over-the-counter medication
- `supplement` - Dietary supplement or vitamin

## Usage Examples

### Creating a Diagnosis:
```bash
POST /api/members/123e4567-e89b-12d3-a456-426614174000/diagnoses
{
  "icdCode": "E11.9",
  "description": "Type 2 diabetes mellitus without complications",
  "clinicalNote": "HbA1c 8.2%, needs better glucose control",
  "status": "active",
  "dateIdentified": "2023-08-15T00:00:00Z",
  "source": "care team",
  "confirmedBy": "Dr. Johnson, MD"
}
```

### Creating a Medication:
```bash
POST /api/members/123e4567-e89b-12d3-a456-426614174000/medications
{
  "medicationName": "Metformin 500mg",
  "rxNormCode": "860975",
  "dosage": "500mg",
  "route": "oral",
  "frequency": "twice daily",
  "startDate": "2023-08-15T00:00:00Z",
  "prescribedBy": "Dr. Johnson, MD",
  "status": "active",
  "adherenceNotes": "Take with meals to reduce GI upset",
  "source": "care team",
  "medicationType": "prescribed"
}
```

### Discontinuing a Medication:
```bash
POST /api/members/123e4567-e89b-12d3-a456-426614174000/medications/456e7890-e89b-12d3-a456-426614174001/discontinue
```

This automatically sets:
- `status` to "discontinued"
- `endDate` to current date

## Integration with Care Plans

These models complement the existing CarePlan Problem model:
- **Diagnosis**: Member-level diagnoses (long-term, ongoing conditions)
- **CarePlan Problems**: Care plan specific problems (episode-based, care coordination focused)
- **Medications**: Member-level medication tracking (comprehensive medication list)

## Testing

Use the provided Postman collection `Member_Diagnoses_Medications_Postman_Collection.json` for comprehensive API testing.

## Migration Setup

To apply these changes:

1. Register migrations in `configure.swift`:
```swift
app.migrations.add(CreateDiagnosis())
app.migrations.add(CreateMedication())
```

2. Run migrations:
```bash
swift run App migrate
```

3. Register controllers in `routes.swift`:
```swift
try app.register(collection: DiagnosesController())
try app.register(collection: MedicationsController())
```
