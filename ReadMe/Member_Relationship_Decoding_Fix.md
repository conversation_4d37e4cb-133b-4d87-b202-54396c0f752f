# Member Relationship Decoding Fix

## Problem Description
**Error**: `Value at path 'member' was not of type 'Member'. Could not decode property.`

This error occurred when trying to create or update Diagnosis and Medication records through the API. The issue was that the models were trying to decode the `member` relationship from the JSON request body, but the client was only sending the data fields, not the full Member object.

## Root Cause
The Diagnosis and Medication models were conforming to `Content` protocol, which automatically includes ALL properties (including relationships) in the JSON encoding/decoding process. When the API received a request like:

```json
{
  "icdCode": "I10",
  "description": "Essential hypertension",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import"
}
```

<PERSON>apor was expecting a `member` field in the JSON because the `@Parent(key: "member_id") var member: Member` property was included in the automatic Content conformance.

## Solution Applied

### 1. **Removed Automatic Content Conformance**
Changed from:
```swift
final class Diagnosis: Model, Content, @unchecked Sendable
```

To:
```swift
final class Diagnosis: Model, @unchecked Sendable
```

### 2. **Added Manual Content Conformance**
Created explicit Content conformance with custom CodingKeys that exclude the `member` relationship:

```swift
extension Diagnosis: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case icdCode = "icd_code"
        case description
        case clinicalNote = "clinical_note"
        case status
        case dateIdentified = "date_identified"
        case source
        case confirmedBy = "confirmed_by"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: member is excluded from coding keys to prevent decoding issues
    }
}
```

### 3. **Created DTOs for Request Handling**
Added Data Transfer Objects to handle API requests:

```swift
struct DiagnosisCreateRequest: Content {
    let icdCode: String?
    let description: String
    let clinicalNote: String?
    let status: String
    let dateIdentified: Date
    let source: String
    let confirmedBy: String?
}
```

### 4. **Updated Controller Methods**
Modified controllers to use DTOs and manually set relationships:

**Before:**
```swift
func createDiagnosis(req: Request) async throws -> Diagnosis {
    var diagnosis = try req.content.decode(Diagnosis.self)
    diagnosis.$member.id = try req.parameters.require("memberID", as: UUID.self)
    try await diagnosis.save(on: req.db)
    return diagnosis
}
```

**After:**
```swift
func createDiagnosis(req: Request) async throws -> Diagnosis {
    let input = try req.content.decode(DiagnosisCreateRequest.self)
    let memberID = try req.parameters.require("memberID", as: UUID.self)
    
    let diagnosis = Diagnosis()
    diagnosis.$member.id = memberID
    diagnosis.icdCode = input.icdCode
    diagnosis.description = input.description
    diagnosis.clinicalNote = input.clinicalNote
    diagnosis.status = input.status
    diagnosis.dateIdentified = input.dateIdentified
    diagnosis.source = input.source
    diagnosis.confirmedBy = input.confirmedBy
    
    try await diagnosis.save(on: req.db)
    return diagnosis
}
```

## Files Modified

### 1. **Sources/App/Models/Member.swift**
- Updated Diagnosis model Content conformance
- Updated Medication model Content conformance
- Added custom CodingKeys for both models

### 2. **Sources/App/Controllers/DiagnosesController.swift**
- Updated createDiagnosis method to use DTO
- Updated updateDiagnosis method to use DTO
- Added DiagnosisCreateRequest DTO

### 3. **Sources/App/Controllers/MedicationsController.swift**
- Updated createMedication method to use DTO
- Updated updateMedication method to use DTO
- Updated MedicationCreateRequest DTO

## Benefits of This Approach

### ✅ **Separation of Concerns**
- API request/response handling is separate from database models
- Relationships are handled explicitly in controllers
- Models remain focused on data structure

### ✅ **Better Error Handling**
- Clear distinction between what's expected in API requests vs database models
- No more confusing "member not found" errors when creating records

### ✅ **API Clarity**
- API consumers know exactly what fields to send
- No confusion about whether to include relationship data

### ✅ **Flexibility**
- Can easily add validation to DTOs
- Can transform data between API and database formats
- Can version DTOs independently of models

## Testing the Fix

### **Before Fix - This Would Fail:**
```bash
POST /api/members/123e4567-e89b-12d3-a456-426614174000/diagnoses
{
  "icdCode": "I10",
  "description": "Essential hypertension",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import"
}
```
**Error**: `Value at path 'member' was not of type 'Member'`

### **After Fix - This Works:**
```bash
POST /api/members/123e4567-e89b-12d3-a456-426614174000/diagnoses
{
  "icdCode": "I10",
  "description": "Essential hypertension",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import"
}
```
**Result**: ✅ Success - Diagnosis created with proper member relationship

## Best Practices Applied

### 1. **DTO Pattern**
Using Data Transfer Objects for API boundaries while keeping models focused on persistence.

### 2. **Explicit Relationship Handling**
Setting parent relationships explicitly in controllers rather than relying on automatic decoding.

### 3. **Custom Content Conformance**
Using custom CodingKeys to control exactly what gets serialized/deserialized.

### 4. **Clear API Contracts**
DTOs provide clear contracts for what data the API expects and returns.

## Prevention for Future Models

When creating new models with relationships, follow this pattern:

1. **Model Definition**: Include relationships but don't auto-conform to Content
2. **Content Extension**: Create explicit Content conformance excluding relationships
3. **DTOs**: Create separate request/response DTOs
4. **Controller Logic**: Use DTOs and set relationships explicitly

This prevents the same decoding issues from occurring with future models.

## Summary

The fix resolves the member decoding error by:
- ✅ Excluding relationships from automatic JSON decoding
- ✅ Using DTOs for clean API contracts
- ✅ Setting relationships explicitly in controllers
- ✅ Maintaining proper separation between API and database concerns

**The Diagnosis and Medication APIs now work correctly without decoding errors!** 🎯
