# Problem Model Title Enhancement Summary

## Overview
Successfully added `title: String?` field to the Problem model in the care plan system, providing better identification and organization of healthcare problems.

## Changes Made

### 1. Model Updates
**File**: `Sources/App/Controllers/CarePlans/CarePlansController.swift`

#### Problem Model (lines 1316-1336):
- Added `@OptionalField(key: "title") var title: String?` as the first field after relationships

#### Content Conformance (lines 1338-1354):
- Added `case title` to CodingKeys enum for proper JSON serialization

### 2. API Request/Response Updates

#### ProblemCreateRequest (lines 1662-1671):
```swift
struct ProblemCreateRequest: Content {
    let title: String?
    let icdCode: String?
    let description: String
    let clinicalNote: String?
    let status: String
    let dateIdentified: Date
    let source: String
    let confirmedBy: String?
}
```

#### Controller Methods Updated:
- **createProblem** (lines 414-423): Added handling for `title` field
- **updateProblem** (lines 797-804): Added handling for `title` field

### 3. Database Migration
**File**: `Sources/App/Migrations/AddTitleToProblem.swift`

```swift
struct AddTitleToProblem: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("problems")
            .field("title", .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("problems")
            .deleteField("title")
            .update()
    }
}
```

#### Migration Registration:
**File**: `Sources/App/configure.swift` (line 250)
- Added `app.migrations.add(AddTitleToProblem())`

### 4. Documentation Updates

#### Postman Collection Updates:
**File**: `CarePlansController_Postman_Collection.json`

**Create Problem Request**:
```json
{
  "title": "Primary Hypertension Management",
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP remains elevated despite medication adjustment.",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

**Update Problem Request**:
```json
{
  "title": "Resolved Primary Hypertension",
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP now controlled with current medication regimen. Patient showing good compliance.",
  "status": "resolved",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

#### README Documentation:
**File**: `CarePlansController_README.md`
- Updated Problem Model example to include `title` field

## API Usage Examples

### Creating a Problem with Title:
```bash
POST /api/careplans/{carePlanID}/problems
Content-Type: application/json

{
  "title": "Primary Hypertension Management",
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP remains elevated despite medication adjustment.",
  "status": "active",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

### Updating a Problem:
```bash
PUT /api/careplans/{carePlanID}/problems/{problemID}
Content-Type: application/json

{
  "title": "Resolved Primary Hypertension",
  "icdCode": "I10",
  "description": "Essential (primary) hypertension",
  "clinicalNote": "BP now controlled with current medication regimen. Patient showing good compliance.",
  "status": "resolved",
  "dateIdentified": "2023-06-01T00:00:00Z",
  "source": "EHR import",
  "confirmedBy": "Dr. Smith, MD"
}
```

## Field Specifications

### title: String? (Optional)
- **Purpose**: Provides a descriptive title for better problem identification and organization
- **Database**: Stored as `title` column in `problems` table
- **API**: Included in JSON as `title`
- **Example**: "Primary Hypertension Management", "Diabetes Type 2 Care", "Chronic Pain Management"
- **Use Case**: Enables easier problem identification in lists, reports, and care plan summaries

## Migration Instructions

1. **Run Migration**: Execute `swift run Run migrate` to apply the database schema changes
2. **Test API**: Use the updated Postman collection to test the new title field
3. **Verify**: Ensure all existing functionality continues to work with the new optional field

## Benefits

1. **Enhanced Organization**: The `title` field provides better identification and categorization of problems
2. **Improved User Experience**: Clearer problem titles make care plans more readable and manageable
3. **Better Reporting**: Titles enable more meaningful problem summaries and reports
4. **Backward Compatibility**: The field is optional, ensuring existing API consumers continue to work
5. **Consistent Design**: Aligns with other care plan entities (CarePlan, Goal, Intervention) that also have title fields

## Status
✅ **Implementation Complete**: All code changes, migrations, and documentation updates have been successfully applied.

## Related Enhancements
This enhancement complements the existing Problem model features:
- ICD code tracking for medical coding
- Clinical notes for detailed documentation
- Status management (active/resolved/inactive)
- Source tracking (EHR import/self-reported/care team)
- Confirmation tracking for accountability
- Member association through MemberProblems relationship
