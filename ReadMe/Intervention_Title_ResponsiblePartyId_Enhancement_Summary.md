# Intervention Model Enhancement Summary

## Overview
Successfully added `title: String?`, `responsiblePartyId: String?`, `status: String?`, and `autoAssignTask: Bool?` fields to the Intervention model in the care plan system, including automatic task creation functionality.

## Changes Made

### 1. Model Updates
**File**: `Sources/App/Controllers/CarePlans/CarePlansController.swift`

#### Intervention Model (lines 1185-1200):
- Added `@OptionalField(key: "title") var title: String?`
- Added `@OptionalField(key: "responsible_party_id") var responsiblePartyId: String?`
- Added `@OptionalField(key: "status") var status: String?`

#### Content Conformance (lines 1202-1216):
- Added `case title` to CodingKeys enum
- Added `case responsiblePartyId = "responsible_party_id"` to CodingKeys enum
- Added `case status` to CodingKeys enum

### 2. API Request/Response Updates

#### InterventionCreateRequest (lines 1569-1581):
```swift
struct InterventionCreateRequest: Content {
    let title: String?
    let action: String
    let responsibleParty: String
    let responsiblePartyId: String?
    let status: String?
    let dueDate: Date
    let autoAssignTask: Bool?

    var shouldAutoAssignTask: Bool {
        return autoAssignTask ?? false
    }
}
```

#### Controller Methods Updated:
- **createIntervention** (lines 453-551): Added handling for `title`, `responsiblePartyId`, `status`, and automatic task creation
- **updateIntervention** (lines 674-679): Added handling for `title`, `responsiblePartyId`, and `status`

#### New Helper Function Added:
- **createTaskFromIntervention** (lines 489-550): Private helper function that automatically creates tasks from interventions when `autoAssignTask` is true

### 3. Database Migration
**File**: `Sources/App/Migrations/AddTitleAndResponsiblePartyIdToIntervention.swift`

```swift
struct AddTitleAndResponsiblePartyIdToIntervention: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("interventions")
            .field("title", .string)
            .field("responsible_party_id", .string)
            .field("status", .string)
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema("interventions")
            .deleteField("title")
            .deleteField("responsible_party_id")
            .deleteField("status")
            .update()
    }
}
```

#### Migration Registration:
**File**: `Sources/App/configure.swift` (line 236)
- Added `app.migrations.add(AddTitleAndResponsiblePartyIdToIntervention())`

### 4. Documentation Updates

#### Postman Collection Updates:
**File**: `CarePlansController_Postman_Collection.json`

**Create Intervention Request (with auto-task assignment)**:
```json
{
  "title": "Medication Management Intervention",
  "action": "Schedule weekly medication review",
  "responsibleParty": "Primary Care Nurse",
  "responsiblePartyId": "nurse-001",
  "status": "active",
  "dueDate": "2025-02-01T00:00:00Z",
  "autoAssignTask": true
}
```

**Update Intervention Request**:
```json
{
  "title": "Updated Medication Management Intervention",
  "action": "Schedule bi-weekly medication review",
  "responsibleParty": "Primary Care Nurse",
  "responsiblePartyId": "nurse-001",
  "status": "in_progress",
  "dueDate": "2025-02-15T00:00:00Z",
  "autoAssignTask": false
}
```

#### README Documentation:
**File**: `CarePlansController_README.md`
- Updated Intervention Model example to include `title`, `responsiblePartyId`, and `status` fields

## API Usage Examples

### Creating an Intervention with Auto-Task Assignment:
```bash
POST /api/careplans/{carePlanID}/interventions
Content-Type: application/json

{
  "title": "Medication Management Intervention",
  "action": "Schedule weekly medication review",
  "responsibleParty": "Primary Care Nurse",
  "responsiblePartyId": "nurse-001",
  "status": "active",
  "dueDate": "2025-02-01T00:00:00Z",
  "autoAssignTask": true
}
```

### Updating an Intervention:
```bash
PUT /api/careplans/{carePlanID}/interventions/{interventionID}
Content-Type: application/json

{
  "title": "Updated Medication Management Intervention",
  "action": "Schedule bi-weekly medication review",
  "responsibleParty": "Primary Care Nurse",
  "responsiblePartyId": "nurse-001",
  "status": "in_progress",
  "dueDate": "2025-02-15T00:00:00Z",
  "autoAssignTask": false
}
```

## Field Specifications

### title: String? (Optional)
- **Purpose**: Provides a descriptive title for the intervention
- **Database**: Stored as `title` column in `interventions` table
- **API**: Included in JSON as `title`
- **Example**: "Medication Management Intervention"

### responsiblePartyId: String? (Optional)
- **Purpose**: Stores an identifier/reference for the responsible party
- **Database**: Stored as `responsible_party_id` column in `interventions` table
- **API**: Included in JSON as `responsible_party_id`
- **Example**: "nurse-001", "doctor-123", "team-456"
- **Use Case**: Can reference user IDs, team IDs, or external system identifiers

### status: String? (Optional)
- **Purpose**: Tracks the current status of the intervention
- **Database**: Stored as `status` column in `interventions` table
- **API**: Included in JSON as `status`
- **Example**: "active", "in_progress", "completed", "cancelled", "on_hold"
- **Use Case**: Enables workflow management and progress tracking of interventions

### autoAssignTask: Bool? (Optional, defaults to false)
- **Purpose**: Controls automatic task creation when creating interventions
- **Database**: Not stored (request-only field)
- **API**: Included in JSON as `autoAssignTask`
- **Default**: `false` when not specified
- **Requirements**: Only creates task if `responsiblePartyId` is provided
- **Task Creation**: Automatically creates a task with:
  - Title: Uses intervention `title` or falls back to `action`
  - Type: "intervention"
  - Status: "pending"
  - Due date: Converted from intervention `dueDate` to Unix timestamp
  - Assigned to: User specified in `responsiblePartyId`
  - Receivers: Member from the care plan
- **Error Handling**: Task creation errors are logged but don't fail intervention creation

## Migration Instructions

1. **Run Migration**: Execute `swift run Run migrate` to apply the database schema changes
2. **Test API**: Use the updated Postman collection to test the new fields
3. **Verify**: Ensure all existing functionality continues to work with the new optional fields

## Benefits

1. **Enhanced Tracking**: The `title` field provides better identification and organization of interventions
2. **Improved Accountability**: The `responsiblePartyId` field enables better tracking and integration with user/team management systems
3. **Workflow Management**: The `status` field enables progress tracking and workflow management of interventions
4. **Automated Task Creation**: The `autoAssignTask` field streamlines care plan workflows by automatically creating tasks
5. **Error Resilience**: Task creation failures don't impact intervention creation, ensuring robust operation
6. **Backward Compatibility**: All fields are optional, ensuring existing API consumers continue to work
7. **Future Integration**: The `responsiblePartyId` field enables future integration with user management, team assignment, and notification systems

## Status
✅ **Implementation Complete**: All code changes, migrations, and documentation updates have been successfully applied.
