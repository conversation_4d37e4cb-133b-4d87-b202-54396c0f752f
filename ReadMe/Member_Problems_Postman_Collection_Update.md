# Member Problems Postman Collection Update

## Overview
The `Member_Diagnoses_Medications_Postman_Collection.json` has been comprehensively updated to include the new Member Problems API endpoints and workflows. The collection now provides complete coverage for managing member-problem relationships alongside existing diagnosis and medication management.

## What's New

### Updated Collection Info
- **Name**: "HMBL Core - Member Diagnoses, Medications, Problems, WHO ICD & RxNav API"
- **Version**: Updated to 4.0.0
- **Description**: Enhanced to include member problems management

### New Variables Added
```json
{
  "problemID": "987fcdeb-51a2-4567-8901-234567890abc",
  "problemAssignmentID": "456789ab-cdef-1234-5678-90abcdef1234"
}
```

## New API Sections

### 1. Member Problems (10 Endpoints)
Complete CRUD operations for member-problem relationships:

#### Core Operations:
- **List Member Problems** - `GET /api/members/{memberID}/problems`
- **Assign Problem to Member** - `POST /api/members/{memberID}/problems`
- **Get Specific Assignment** - `GET /api/members/{memberID}/problems/{assignmentID}`
- **Update Assignment** - `PUT /api/members/{memberID}/problems/{assignmentID}`
- **Remove Assignment** - `DELETE /api/members/{memberID}/problems/{assignmentID}`

#### Advanced Operations:
- **List Active Problems** - `GET /api/members/{memberID}/problems/active`
- **List Inactive Problems** - `GET /api/members/{memberID}/problems/inactive`
- **Search Problems** - `GET /api/members/{memberID}/problems/search`
- **Bulk Assign Problems** - `POST /api/members/{memberID}/problems/bulk`
- **Bulk Deactivate Problems** - `PUT /api/members/{memberID}/problems/bulk/deactivate`

### 2. Member Problems Examples (8 Real-World Scenarios)
Practical examples demonstrating common healthcare workflows:

#### Clinical Scenarios:
- **Assign Chronic Conditions** - Hypertension with detailed clinical notes
- **Assign Multiple Comorbidities** - Diabetes, hyperlipidemia, and obesity
- **Search Cardiovascular Problems** - Finding related conditions
- **Search by ICD Code** - Diabetes using E11 code
- **Update Problem Status** - Marking acute conditions as resolved
- **Care Transition Updates** - Specialist referral documentation
- **Mental Health Assignment** - Depression with safety assessment
- **Preventive Care Problems** - Overdue screening management

### 3. Comprehensive Care Workflow (6 Steps)
Complete integrated workflow demonstrating:
1. **Assign Problem from Care Plan** - Link care plan problems to member
2. **Create Member Diagnosis** - Formal diagnosis based on problem
3. **Search RxNav for Medication** - Find appropriate treatment
4. **Create Medication with Problem Reference** - Link medication to condition
5. **Update Problem with Treatment Plan** - Document comprehensive care
6. **Verify Complete Care Record** - Confirm integrated record

## Key Features

### Rich Problem Assignment Data
Each problem assignment includes comprehensive metadata:
```json
{
  "id": "uuid",
  "assignedDate": "2025-06-22T10:00:00Z",
  "assignedBy": "Dr. Smith, MD",
  "notes": "Patient reports ongoing symptoms, needs monitoring",
  "isActive": true,
  "createdAt": "2025-06-22T10:00:00Z",
  "updatedAt": "2025-06-22T10:00:00Z",
  "problem": {
    "id": "uuid",
    "icdCode": "I10",
    "description": "Essential (primary) hypertension",
    "clinicalNote": "BP remains elevated despite medication adjustment",
    "status": "active",
    "dateIdentified": "2025-06-20T00:00:00Z",
    "source": "EHR import",
    "confirmedBy": "Dr. Johnson, MD"
  }
}
```

### Advanced Search and Filtering
Multiple search parameters supported:
- **Text Search**: `?q=hypertension` - Search problem descriptions and notes
- **Status Filter**: `?status=active` - Filter by assignment status
- **ICD Code Filter**: `?icd_code=I10` - Filter by specific ICD codes
- **Combined Filters**: Multiple parameters can be used together

### Bulk Operations
Efficient management of multiple problems:
- **Bulk Assignment**: Assign multiple problems in one request
- **Bulk Deactivation**: Deactivate multiple assignments efficiently
- **Array-based requests**: Support for batch operations

### Real-World Clinical Examples
Practical scenarios covering:
- **Chronic Disease Management**: Diabetes, hypertension, heart failure
- **Mental Health**: Depression with safety protocols
- **Preventive Care**: Screening reminders and follow-ups
- **Care Transitions**: Specialist referrals and handoffs
- **Acute Conditions**: Resolution tracking and documentation

## Integration Benefits

### 1. Complete Care Coordination
- **Problem Assignment**: Link care plan problems to member records
- **Diagnosis Creation**: Formal diagnosis based on assigned problems
- **Medication Management**: Prescriptions linked to specific conditions
- **Treatment Tracking**: Comprehensive care documentation

### 2. Clinical Workflow Support
- **Care Team Collaboration**: Track who assigned what and when
- **Status Management**: Active/inactive problem tracking
- **Progress Documentation**: Detailed notes for each assignment
- **Historical Tracking**: Complete assignment history

### 3. Population Health Management
- **Condition Identification**: Find members with specific problems
- **Care Gap Analysis**: Identify missing assignments or treatments
- **Outcome Tracking**: Monitor problem resolution across populations
- **Quality Metrics**: Support for clinical quality measures

### 4. Seamless Integration
- **Existing Systems**: Works with current diagnosis and medication APIs
- **Care Plans**: Leverages existing problem definitions
- **RxNav Integration**: Medication search tied to problem management
- **WHO ICD Integration**: Standardized coding support

## Usage Examples

### 1. Assign Chronic Condition
```json
POST /api/members/{memberID}/problems
{
  "problemId": "uuid",
  "assignedBy": "Dr. Martinez, MD",
  "notes": "Essential hypertension. BP >140/90. Started ACE inhibitor. Monthly monitoring required."
}
```

### 2. Search Active Cardiovascular Problems
```http
GET /api/members/{memberID}/problems/search?q=cardiovascular&status=active
```

### 3. Bulk Assign Comorbidities
```json
POST /api/members/{memberID}/problems/bulk
[
  {
    "problemId": "diabetes-uuid",
    "assignedBy": "Dr. Johnson, MD",
    "notes": "Type 2 DM - HbA1c 8.2%"
  },
  {
    "problemId": "hyperlipidemia-uuid", 
    "assignedBy": "Dr. Johnson, MD",
    "notes": "LDL 165 mg/dL - started statin"
  }
]
```

### 4. Update Problem Status
```json
PUT /api/members/{memberID}/problems/{assignmentID}
{
  "notes": "Acute bronchitis resolved after antibiotics. No more symptoms.",
  "isActive": false
}
```

## Testing the Collection

1. **Import** the updated collection into Postman
2. **Set Variables**: Update `baseUrl`, `authToken`, `memberID`, etc.
3. **Test Core Operations**: Try basic CRUD operations
4. **Run Examples**: Execute real-world scenarios
5. **Test Workflows**: Follow the comprehensive care workflow
6. **Validate Integration**: Verify problems work with diagnoses and medications

## API Authentication

All member problems endpoints require authentication:
```
Authorization: Bearer {{authToken}}
```

## Collection Structure

The updated collection now includes:
- **4 Main Sections**: Diagnoses, Medications, Problems, WHO ICD, RxNav
- **3 Example Sections**: Real-world scenarios for each main section
- **2 Workflow Sections**: Medication workflow and comprehensive care workflow
- **50+ Endpoints**: Complete API coverage
- **Rich Documentation**: Detailed descriptions and examples

## Migration from Previous Version

### Version 3.0.0 → 4.0.0 Changes:
- ✅ Added Member Problems section (10 endpoints)
- ✅ Added Member Problems Examples (8 scenarios)
- ✅ Added Comprehensive Care Workflow (6 steps)
- ✅ Updated collection metadata and variables
- ✅ Enhanced integration examples

### Backward Compatibility:
- ✅ All existing endpoints remain unchanged
- ✅ Existing variables and examples still work
- ✅ No breaking changes to current workflows

The updated Postman collection now provides **complete coverage** for the integrated member healthcare management system, supporting problems, diagnoses, medications, and external API integrations in a unified, production-ready testing environment! 🚀
