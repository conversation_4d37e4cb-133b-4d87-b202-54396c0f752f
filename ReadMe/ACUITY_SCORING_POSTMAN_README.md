# Acuity Scoring API - Postman Collection

This Postman collection provides comprehensive testing for the Acuity Scoring feature, including all endpoints, error cases, and NCQA compliance scenarios.

## 🚀 Quick Setup

### 1. Import Files
1. Import `Acuity_Scoring_API.postman_collection.json` into Postman
2. Import `Acuity_Scoring_Environment.postman_environment.json` as an environment
3. Select the "Acuity Scoring Environment" in Postman

### 2. Configure Environment Variables
Update these variables in your environment:

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | Your API base URL | `http://localhost:8080` |
| `username` | Your login username | `<EMAIL>` |
| `password` | Your login password | `password123` |
| `member_id` | Valid member UUID for testing | `12345678-1234-1234-1234-123456789012` |

## 📋 Collection Structure

### 1. Authentication
- **Login**: Authenticates and auto-extracts auth token

### 2. Acuity Scoring
- **Create Acuity Score**: Creates high-risk score with multiple references
- **Create Acuity Score - Manual Entry**: Creates low-risk score with manual source
- **Get Latest Acuity Score**: Retrieves most recent acuity score
- **Get Acuity History**: Retrieves complete acuity history

### 3. Error Cases
- **Invalid Member ID**: Tests error handling for bad UUIDs
- **Invalid Level**: Tests validation for invalid acuity levels
- **No Acuity Score Found**: Tests 404 response for members without scores

## 🎯 Testing Scenarios

### High-Risk Member (Score: 7, Level: High)
```json
{
  "score": 7,
  "level": "High",
  "source": "AssessmentEngine",
  "notes": "Multiple indicators from recent assessments",
  "references": [
    {
      "type": "assessment",
      "ref_id": "uuid",
      "title": "PHQ-9",
      "reason": "PHQ-9 score of 18 indicates severe depression"
    },
    {
      "type": "hospitalization", 
      "ref_id": "uuid",
      "title": "ER Visit",
      "reason": "3 ER visits in 60 days"
    }
  ]
}
```

### Low-Risk Member (Score: 3, Level: Low)
```json
{
  "score": 3,
  "level": "Low", 
  "source": "Manual",
  "notes": "Member showing improvement, stable condition",
  "references": [
    {
      "type": "careplan",
      "ref_id": "uuid",
      "title": "Diabetes Management Plan",
      "reason": "Following care plan successfully"
    }
  ]
}
```

## 🔧 Auto-Features

### Automatic Token Management
- Login request automatically extracts and stores auth token
- All subsequent requests use the stored token

### UUID Generation
- Reference IDs are auto-generated for testing
- No need to manually create UUIDs

### Environment Variables
- Pre-configured with sensible defaults
- Easy to customize for different environments

## 📊 Expected Responses

### Create Acuity Score (201 Created)
```json
{
  "id": "uuid",
  "score": 7,
  "level": "High",
  "source": "AssessmentEngine",
  "notes": "Multiple indicators from recent assessments",
  "references": [...],
  "createdByUser": {
    "id": "uuid",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>"
  },
  "createdAt": "2025-09-25T10:30:00Z",
  "updatedAt": "2025-09-25T10:30:00Z"
}
```

### Get Acuity History (200 OK)
```json
[
  {
    "score": 7,
    "level": "High",
    "createdAt": "2025-09-25T10:30:00Z",
    "source": "AssessmentEngine"
  },
  {
    "score": 5,
    "level": "Moderate", 
    "createdAt": "2025-09-20T14:15:00Z",
    "source": "Manual"
  }
]
```

## ⚠️ Error Responses

### Invalid Level (400 Bad Request)
```json
{
  "error": true,
  "reason": "Invalid level. Must be Low, Moderate, or High"
}
```

### Member Not Found (404 Not Found)
```json
{
  "error": true,
  "reason": "Member not found"
}
```

### No Acuity Score (404 Not Found)
```json
{
  "error": true,
  "reason": "No acuity score found for member"
}
```

## 🏥 NCQA Compliance Testing

This collection tests all NCQA requirements:

✅ **Source Traceability**: Every score includes detailed references  
✅ **Audit Trail**: Timeline logging captures all changes  
✅ **Risk Stratification**: Low/Moderate/High levels supported  
✅ **Clinical Justification**: Notes and reasons for each reference  
✅ **Historical Tracking**: Complete history endpoint  

## 🔄 Workflow Testing

1. **Run Authentication** → Login to get token
2. **Create High-Risk Score** → Test assessment engine integration
3. **Get Latest Score** → Verify creation
4. **Create Manual Score** → Test care team workflow
5. **Get History** → Verify historical tracking
6. **Test Error Cases** → Verify proper error handling

## 🚨 Pro Tips

- **Run in sequence** for best results
- **Check timeline items** in your database after creating scores
- **Verify references** are properly stored as JSON
- **Test with real member UUIDs** from your database
- **Monitor logs** for timeline creation

This collection covers every aspect of the acuity scoring feature and ensures NCQA compliance! 🎉
