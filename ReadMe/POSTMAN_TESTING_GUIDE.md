# 🚀 Background Check System - Postman Testing Guide

## 📦 **What's Included**

- **`Background_Check_System.postman_collection.json`** - Complete API collection
- **`Background_Check_Environment.postman_environment.json`** - Environment variables
- **This guide** - Step-by-step testing instructions

---

## 🔧 **Setup Instructions**

### 1. **Import Collection & Environment**
```bash
# In Postman:
1. Click "Import" button
2. Drag & drop both JSON files
3. Select "Background Check Environment" from dropdown
```

### 2. **Configure Environment Variables**
```bash
# Required Variables:
baseUrl          = http://localhost:8080
adminToken       = your_admin_bearer_token_here
memberId         = valid_member_uuid_here
sessionCookie    = your_admin_session_cookie

# Auto-populated:
queueItemId      = (set automatically by tests)
timestamp        = (set automatically)
```

### 3. **Get Admin Token**
```bash
# Method 1: Login via API
POST /api/auth/login
{
    "email": "<EMAIL>",
    "password": "your_password"
}

# Method 2: Extract from browser dev tools
# Login to admin dashboard, check Network tab for Authorization header
```

---

## 🎯 **Testing Workflow**

### **Phase 1: Trigger Background Check**
```bash
1. Set valid memberId in environment
2. Run "Trigger Background Check"
3. Check response:
   - success: true (no matches found)
   - queueItemId: uuid (matches found - needs review)
```

### **Phase 2: Review Queue Management**
```bash
1. Run "Get Review Queue" 
   → See all pending reviews
2. Run "Get Queue Item Details"
   → Detailed match information
3. Choose approval path:
   → "Approve Background Check" OR
   → "Reject Background Check"
```

### **Phase 3: Admin UI Testing**
```bash
1. Set sessionCookie in environment
2. Run "Background Check Queue Dashboard"
   → Visual interface for queue management
3. Run "Admin Dashboard"
   → Main admin panel with queue access
```

---

## 🧪 **Test Scenarios**

### **Scenario A: Clean Background Check**
```json
{
    "fullName": "Jane Clean Record",
    "zipCode": "90210"
}
```
**Expected:** Direct approval, no queue item created

### **Scenario B: Potential Match Found**
```json
{
    "fullName": "John Criminal History",
    "zipCode": "12345"
}
```
**Expected:** Queue item created for admin review

### **Scenario C: Invalid Data**
```json
{
    "fullName": "",
    "zipCode": "invalid"
}
```
**Expected:** Validation errors returned

---

## 🔍 **Response Examples**

### **Successful Trigger (No Matches)**
```json
{
    "success": true,
    "message": "Background check completed - no matches found",
    "backgroundCheckAdded": true
}
```

### **Matches Found (Queue Created)**
```json
{
    "success": true,
    "message": "Background check completed - matches found, added to review queue",
    "queueItemId": "550e8400-e29b-41d4-a716-************",
    "matchCount": 2,
    "riskLevel": "medium"
}
```

### **Queue Item Details**
```json
{
    "id": "550e8400-e29b-41d4-a716-************",
    "member": {
        "id": "member-uuid",
        "firstName": "John",
        "lastName": "Doe"
    },
    "searchQuery": {
        "fullName": "John Doe",
        "zipCode": "12345"
    },
    "matchCount": 2,
    "riskLevel": "medium",
    "rawResponse": {
        "matches": [...]
    },
    "createdAt": "2025-09-09T12:00:00Z"
}
```

---

## 🚨 **Troubleshooting**

### **401 Unauthorized**
```bash
Problem: Invalid or expired admin token
Solution: 
1. Re-login to get fresh token
2. Update adminToken in environment
3. Ensure user has admin/drop_all role
```

### **403 Forbidden**
```bash
Problem: User lacks admin privileges
Solution:
1. Verify user role in database
2. Check middleware configuration
3. Ensure proper authentication flow
```

### **404 Not Found**
```bash
Problem: Invalid member ID or queue item ID
Solution:
1. Verify member exists in database
2. Check UUID format
3. Ensure queue item hasn't been processed
```

### **500 Internal Server Error**
```bash
Problem: Server-side issues
Solution:
1. Check server logs
2. Verify ZYLA_API_KEY environment variable
3. Ensure database migrations are run
4. Check Zyla API service status
```

---

## 🔐 **Security Notes**

- **Admin Only**: All endpoints require admin authentication
- **Audit Trail**: Every action creates timeline entries
- **Data Privacy**: Sensitive data is properly encrypted
- **Rate Limiting**: API calls are rate-limited for security

---

## 📊 **Performance Testing**

### **Load Testing Endpoints**
```bash
# Test concurrent background checks
POST /api/background-checks/members/:id/trigger

# Test queue performance
GET /api/background-checks/queue?page=1&per=100

# Monitor response times and error rates
```

---

## 🎉 **Success Criteria**

✅ **All API endpoints return expected responses**  
✅ **Authentication and authorization work correctly**  
✅ **Queue management functions properly**  
✅ **Admin UI loads and functions**  
✅ **Error handling provides clear messages**  
✅ **Timeline audit logging works**  
✅ **Security tags are applied correctly**

---

## 🆘 **Support**

If you encounter issues:
1. Check server logs: `tail -f logs/app.log`
2. Verify environment variables: `echo $ZYLA_API_KEY`
3. Test database connection: `swift run App migrate --dry-run`
4. Review Postman console for detailed error messages

**Happy Testing!** 🚀
