# RxNav API Postman Collection Update

## Overview
The `Member_Diagnoses_Medications_Postman_Collection.json` has been updated to include comprehensive RxNav API endpoints for medication search and management. The collection now provides a complete workflow for searching medications, getting detailed drug information, and integrating with the existing medication management system.

## What's New

### Updated Collection Info
- **Name**: Changed to "HMBL Core - Member Diagnoses, Medications, WHO ICD & RxNav API"
- **Version**: Updated to 3.0.0
- **Description**: Enhanced to include RxNav API integration

### New Variables Added
```json
{
  "rxcui": "617314",
  "medicationName": "lisinopril", 
  "brandName": "Lipitor",
  "ingredientName": "atorvastatin"
}
```

## New API Sections

### 1. RxNav API (8 endpoints)
Complete set of RxNav API endpoints for medication search and management:

#### Core Endpoints:
- **Health Check** - `GET /rxnav/health`
- **Search Medications (Basic)** - `POST /rxnav/search`
- **Search Medications (With Details)** - `POST /rxnav/search` (includeDetails: true)
- **Search by Brand Name** - `POST /rxnav/search/brand`
- **Search by Ingredient** - `POST /rxnav/search/ingredient`
- **Get Medication Details** - `POST /rxnav/details`
- **Spelling Suggestions** - `POST /rxnav/search/spelling`
- **Approximate Search** - `POST /rxnav/search/approximate`

### 2. RxNav Examples (5 endpoints)
Real-world medication search examples:
- Blood Pressure Medications (lisinopril)
- Cholesterol Medications (Lipitor brand search)
- Pain Medications (ibuprofen generic search)
- Diabetes Medications (metformin with details)
- Specific Medication Details (RXCUI: 617314)

### 3. Medication Workflow (RxNav Integration) (4 steps)
Complete workflow demonstrating RxNav API integration:
1. **Search RxNav for Medication** - Find medications and RXCUIs
2. **Get Detailed Information** - Retrieve comprehensive medication data
3. **Create Medication with RxNav Data** - Use API data to create medication records
4. **Verify Created Medication** - Confirm successful creation

### 4. Enhanced Member Medications
Added new example showing RxNav integration:
- **Create Medication (RxNav Example)** - Shows how to use RxNav data in medication creation

## Key Features

### Comprehensive Medication Search
- **Basic Search**: Find medications by name (generic and branded)
- **Brand Search**: Filter to only branded medications
- **Ingredient Search**: Filter to only generic medications
- **Detailed Search**: Include enhanced properties (prescribability, schedule, etc.)

### Advanced Search Features
- **Spelling Suggestions**: Handle typos in medication names
- **Approximate Search**: Partial name matching for autocomplete
- **Health Check**: Verify RxNav API connectivity

### Rich Medication Data
Each medication result includes:
```json
{
  "rxcui": "617314",
  "fullName": "atorvastatin 10 MG Oral Tablet [Lipitor]",
  "strength": "10 MG",
  "doseForm": "Oral Tablet", 
  "route": "Oral",
  "displayName": "Lipitor 10 MG Oral Tablet",
  "brandName": "Lipitor",
  "isGeneric": false,
  "isPrescribable": true,
  "schedule": "0",
  "humanDrug": "US"
}
```

### Detailed Medication Properties
The medication details endpoint provides:
- **Properties**: All medication attributes and classifications
- **Codes**: NDC, SPL_SET_ID, MMSL_CODE, etc.
- **Sources**: Data source information
- **Enhanced Metadata**: Prescribability, scheduling, human drug classification

## Usage Examples

### 1. Basic Medication Search
```json
POST /rxnav/search
{
  "name": "lisinopril",
  "includeDetails": false
}
```

### 2. Brand Name Search
```json
POST /rxnav/search/brand
{
  "name": "Lipitor",
  "includeDetails": false  
}
```

### 3. Get Medication Details
```json
POST /rxnav/details
{
  "rxcui": "617314"
}
```

### 4. Create Medication with RxNav Data
```json
POST /api/members/{memberID}/medications
{
  "medicationName": "atorvastatin 20 MG Oral Tablet",
  "rxNormCode": "617310",
  "rxcui": "617310",
  "dosage": "20 MG",
  "route": "Oral",
  "doseForm": "Tablet",
  "isGeneric": true,
  "isPrescribable": true,
  "strength": "20 MG",
  "tty": "SCD",
  "source": "RxNav search"
}
```

## Integration Benefits

### 1. Standardized Medication Data
- Uses official RxNav RXCUIs for medication identification
- Provides standardized medication names and classifications
- Ensures consistency across the healthcare system

### 2. Enhanced User Experience
- Spelling correction for medication searches
- Autocomplete functionality with approximate search
- Comprehensive medication information at your fingertips

### 3. Clinical Decision Support
- Prescribability information
- Drug scheduling classification
- Generic vs. branded medication identification
- Comprehensive medication properties and codes

### 4. Seamless Workflow
- Search → Details → Create workflow
- Real-world examples for common medications
- Integration with existing Member Medications system

## Testing the Collection

1. **Import** the updated collection into Postman
2. **Set variables**: Update `baseUrl`, `authToken`, and other variables as needed
3. **Test Health Check**: Verify RxNav API connectivity
4. **Run Examples**: Try the medication search examples
5. **Follow Workflows**: Use the step-by-step medication workflow

## API Authentication

All RxNav endpoints require the same authentication as other HMBL Core APIs:
```
Authorization: Bearer {{authToken}}
```

The RxNav API integration is now fully functional and ready for production use! 🚀
