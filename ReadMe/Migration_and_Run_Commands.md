# Migration and Run Commands Guide

## Correct Executable Name
The executable target in this project is named **"Run"** (not "App").

From `Package.swift` line 52:
```swift
.executableTarget(name: "<PERSON>", dependencies: [.target(name: "App")])
```

## Database Migration Commands

### **Run All Pending Migrations:**
```bash
swift run Run migrate
```

### **Run Migrations with Environment:**
```bash
# Development (default)
swift run Run migrate

# Production
swift run Run migrate --env production

# Staging
swift run Run migrate --env staging
```

### **Dry Run (Preview migrations without executing):**
```bash
swift run Run migrate --dry-run
```

### **Revert Last Migration:**
```bash
swift run Run migrate --revert
```

### **Check Migration Status:**
```bash
swift run Run migrate --status
```

## Application Run Commands

### **Start the Server:**
```bash
# Development mode
swift run Run serve

# Development with debug logging
swift run Run serve --log debug

# Production mode
swift run Run serve --env production

# Custom hostname and port
swift run Run serve --hostname 0.0.0.0 --port 8080
```

### **Run Background Queues:**
```bash
swift run Run queues
```

### **Production Command (from Procfile):**
```bash
# Web server
swift run Run serve --log debug --env production --hostname 0.0.0.0 --port $PORT

# Background worker
swift run Run queues
```

## Docker Commands (Alternative)

If you prefer using Docker:

### **Build and Run with Docker Compose:**
```bash
# Start all services (app + database)
docker-compose up

# Run migrations in Docker
docker-compose exec app swift run Run migrate

# Build and start in background
docker-compose up -d
```

### **Docker Migration Commands:**
```bash
# Run migrations
docker-compose exec app swift run Run migrate

# Dry run migrations
docker-compose exec app swift run Run migrate --dry-run

# Revert migrations
docker-compose exec app swift run Run migrate --revert
```

## Fixing the Goal PostgreSQL Error

To fix the Goal creation error, run the migration with the corrected CreateGoal migration:

### **Step 1: Run Migration**
```bash
swift run Run migrate
```

### **Step 2: Verify Migration**
Check that the goals table was created with timestamp fields:
```sql
# Connect to your PostgreSQL database
psql -h localhost -U your_username -d your_database

# Check goals table structure
\d goals

# Should show columns including:
# - created_at (timestamp)
# - updated_at (timestamp)
```

### **Step 3: Test Goal Creation**
Use your Postman collection to test creating a goal:
```bash
POST /api/careplans/{carePlanID}/goals
{
  "description": "Improve medication adherence",
  "type": "health",
  "targetDate": "2025-06-01T00:00:00Z",
  "status": "active",
  "objective": "Patient will take prescribed medications as directed",
  "measurementCriteria": "90% medication adherence rate"
}
```

## Required Migrations for This Project

Based on our recent changes, you need to run migrations for:

1. **CreateDiagnosis** - Member diagnoses
2. **CreateMedication** - Member medications  
3. **CreateProblemUpdate** - Enhanced Problem model
4. **TimelineItemCarePlanMigration** - Timeline items for care plans
5. **CreateGoal** - Fixed Goal model with timestamps

### **Run All Migrations:**
```bash
swift run Run migrate
```

This will apply all pending migrations in the correct order.

## Troubleshooting

### **If Migration Fails:**
```bash
# Check migration status
swift run Run migrate --status

# See what migrations are pending
swift run Run migrate --dry-run

# Check database connection
swift run Run serve --log debug
```

### **If Database Connection Issues:**
Check your environment variables:
- `DATABASE_URL` or individual DB settings
- `DATABASE_HOST`
- `DATABASE_PORT`
- `DATABASE_USERNAME`
- `DATABASE_PASSWORD`
- `DATABASE_NAME`

### **If Build Issues:**
```bash
# Clean build
swift package clean

# Resolve dependencies
swift package resolve

# Build
swift build

# Run
swift run Run migrate
```

## Environment Configuration

### **Development (.env.development):**
```bash
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password
DATABASE_NAME=hmbl_core_dev
```

### **Production:**
```bash
DATABASE_URL=postgresql://username:password@host:port/database
```

## Common Commands Summary

| Task | Command |
|------|---------|
| **Run Migrations** | `swift run Run migrate` |
| **Start Server** | `swift run Run serve` |
| **Debug Mode** | `swift run Run serve --log debug` |
| **Production** | `swift run Run serve --env production` |
| **Background Jobs** | `swift run Run queues` |
| **Dry Run Migration** | `swift run Run migrate --dry-run` |
| **Revert Migration** | `swift run Run migrate --revert` |
| **Build Project** | `swift build` |
| **Clean Build** | `swift package clean` |

## Next Steps

1. **Run the migration** to fix the Goal PostgreSQL error:
   ```bash
   swift run Run migrate
   ```

2. **Start the server** to test the fixes:
   ```bash
   swift run Run serve --log debug
   ```

3. **Test all endpoints** using your Postman collections

4. **Monitor logs** for any remaining issues

The corrected executable name should resolve the migration error, and running the migration will fix the Goal creation PostgreSQL error! 🚀
