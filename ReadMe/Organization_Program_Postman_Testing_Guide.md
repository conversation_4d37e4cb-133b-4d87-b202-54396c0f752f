# Organization Program Setup API - Postman Testing Guide

## 🚀 Quick Setup

### 1. Import the Collection
1. Open Postman
2. Click **Import** button
3. Select `Organization_Program_Setup_Postman_Collection.json`
4. Collection will be imported with all endpoints and examples

### 2. Set Environment Variables
The collection uses these variables (set in Postman Environment or Collection Variables):

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `base_url` | `http://localhost:8080` | Your API base URL |
| `org_id` | `org_roseman_genesis` | Organization ID for testing |
| `program_key` | `ltss` | Program key for testing |
| `auth_token` | *(empty)* | Your authentication token |

### 3. Set Your Auth Token
- Go to **Environments** in Postman
- Set the `auth_token` variable with your actual authentication token
- Or update it in the Collection Variables

## 📋 Test Scenarios

### Scenario 1: Complete CRUD Workflow
**Test the full lifecycle of a program:**

1. **Create Program** - Creates LTSS program
2. **Get All Programs** - Verify program appears in list
3. **Get Single Program** - Fetch the specific program
4. **Update Program** - Modify program configuration
5. **Get Single Program** - Verify updates were applied
6. **Delete Program** - Remove the program
7. **Get All Programs** - Verify program is deleted

### Scenario 2: Multiple Program Types
**Test different program configurations:**

1. **Create Mental Health Program** - Different assessment types
2. **Create Program** - LTSS program
3. **Get All Programs** - Should show both programs

### Scenario 3: Error Handling
**Test error conditions:**

1. **Create Program** with duplicate `programKey` - Should return 409 Conflict
2. **Get Single Program** with invalid `program_key` - Should return 404 Not Found
3. **Update Program** with invalid `program_key` - Should return 404 Not Found
4. **Delete Program** with invalid `program_key` - Should return 404 Not Found

## 🧪 Built-in Tests

Each request includes automatic tests that verify:

✅ **Response Time** - Less than 2000ms  
✅ **Content Type** - Correct JSON response  
✅ **Status Codes** - Appropriate HTTP status  
✅ **Variable Storage** - Automatically stores program data for chaining requests  

## 📊 Expected Responses

### Create Program (201 Created)
```json
{
  "id": "uuid-here",
  "org_id": "org_roseman_genesis",
  "program_name": "Long-Term Services and Supports",
  "program_key": "ltss",
  "program_config": {
    "orgId": "org_roseman_genesis",
    "templateId": "ltss_template_001",
    // ... full config object
  },
  "created_at": "2025-09-21T...",
  "updated_at": "2025-09-21T..."
}
```

### Get All Programs (200 OK)
```json
[
  {
    "id": "uuid-here",
    "org_id": "org_roseman_genesis",
    "program_name": "Long-Term Services and Supports",
    "program_key": "ltss",
    // ... program details
  }
]
```

### Error Responses

#### 409 Conflict (Duplicate Program Key)
```json
{
  "error": true,
  "reason": "Program key already exists for this organization"
}
```

#### 404 Not Found
```json
{
  "error": true,
  "reason": "Program not found"
}
```

#### 400 Bad Request
```json
{
  "error": true,
  "reason": "Invalid organization ID or program key"
}
```

## 🔧 Customization

### Adding New Test Programs
Copy the "Create Program" request and modify:

1. Change `programName` and `programKey`
2. Update `programConfig` with different:
   - `programType`
   - `requiredAssessments`
   - `programReviews`
   - `reviewFrequencyDays`

### Example Custom Program
```json
{
  "programName": "Diabetes Management Program",
  "programKey": "diabetes_mgmt",
  "programConfig": {
    "orgId": "{{org_id}}",
    "templateId": "diabetes_template_001",
    "programType": "CHRONIC_CARE",
    "displayName": "Diabetes Management Program",
    "description": "Comprehensive diabetes care management program",
    "reviewFrequencyDays": 45,
    "daysToCompleteAssessment": 7,
    "requiredAssessments": [
      {
        "itemKey": "hba1c",
        "title": "HbA1c Test",
        "required": true
      },
      {
        "itemKey": "foot_exam",
        "title": "Diabetic Foot Examination",
        "required": true
      }
    ],
    "programReviews": [
      {
        "itemKey": "diabetes_review",
        "title": "Diabetes Management Review",
        "required": true
      }
    ]
  }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **401 Unauthorized** - Check your `auth_token` variable
2. **404 Not Found** - Verify `org_id` and `program_key` variables
3. **Connection Refused** - Ensure your API server is running on the correct port
4. **500 Internal Server Error** - Check server logs for database connection issues

### Debug Tips

1. **Check Variables** - Use `{{variable_name}}` to see resolved values
2. **Console Logs** - Add `console.log()` in test scripts for debugging
3. **Response Body** - Always check the full response for error details

## 🎯 Pro Tips

1. **Run Collection** - Use Postman's Collection Runner for automated testing
2. **Environment Switching** - Create different environments for dev/staging/prod
3. **Data Files** - Use CSV files for bulk testing with different org IDs
4. **Monitors** - Set up Postman Monitors for continuous API health checks

Happy testing! 🚀
