# Consent Creator

A comprehensive admin tool for creating and managing digital consent forms with a visual form builder interface. The Consent Creator allows administrators to build custom consent forms with various section types, styling options, and signature blocks.

## Features

### Visual Form Builder
- **Drag & Drop Interface**: Intuitive section building with drag-and-drop functionality
- **Live Preview**: Real-time preview of consent forms as you build them
- **Toggleable Preview**: Show/hide preview panel to maximize builder space
- **Section Reordering**: Drag sections to reorder them within the form

### Section Types
- **Text Sections**: Add informational text content with emphasis options
- **List Sections**: Create bulleted lists for terms, conditions, or procedures
- **Signature Blocks**: Add signature fields with participant name and date

### Customization Options
- **Header Configuration**: Title, subtitle, and organization branding
- **Styling Controls**: Primary color customization and font family selection
- **Section Properties**: Emphasis highlighting and required field toggles
- **Auto-Generated Keys**: Automatic key generation from consent form names

### Management Features
- **CRUD Operations**: Create, read, update, and delete consent forms
- **Status Management**: Draft, active, and inactive status options
- **Multi-language Support**: English, Spanish, and French language options
- **Organization Isolation**: Forms are isolated by organization for security

## Technical Implementation

### Backend Components

#### ConsentCreatorController
- **Location**: `Sources/App/Controllers/ConsentCreatorController.swift`
- **Routes**: All routes are protected with `AdminAuthMiddleware()`
- **Base Path**: `/admin/consent-creator`

**Route Structure:**
```
GET    /admin/consent-creator                    # Dashboard
GET    /admin/consent-creator/create             # Create form
POST   /admin/consent-creator/create             # Save new form
GET    /admin/consent-creator/:id/edit           # Edit form
PUT    /admin/consent-creator/:id                # Update form
DELETE /admin/consent-creator/:id                # Delete form
GET    /admin/consent-creator/:id/preview        # Preview form
```

#### Data Storage
- **Model**: Uses existing `Template` model with `kind = "consent_web"`
- **JSON Storage**: Consent form structure stored as JSON using `JsonWrapper`
- **Database**: PostgreSQL with JSONB field for efficient JSON operations

### Frontend Components

#### Templates
- **Dashboard**: `Resources/Views/consent-creator-dashboard.leaf`
- **Form Builder**: `Resources/Views/consent-creator-form.leaf`

#### JavaScript Features
- **SortableJS**: Drag-and-drop functionality for section reordering
- **Real-time Preview**: Live updates as sections are modified
- **Form Validation**: Client-side validation before submission
- **Error Handling**: Comprehensive error messages and fallback behavior

### JSON Structure

The consent form data is stored as JSON with the following structure:

```json
{
  "header": {
    "title": "Consent Form Title",
    "subtitle": "Optional subtitle",
    "organization": "Organization Name"
  },
  "styling": {
    "fontFamily": "Arial, sans-serif",
    "primaryColor": "#3B82F6"
  },
  "metadata": {
    "id": "unique-identifier",
    "version": "1.0",
    "description": "Form description",
    "fileNamePrefix": "consent_form"
  },
  "sections": [
    {
      "id": "section_1",
      "type": "text",
      "title": "Section Title",
      "content": "Section content text",
      "emphasis": false,
      "required": true
    },
    {
      "id": "section_2",
      "type": "list",
      "title": "List Section",
      "content": ["Item 1", "Item 2", "Item 3"],
      "emphasis": true,
      "required": false
    },
    {
      "id": "section_3",
      "type": "signature",
      "title": "Signature",
      "content": "Please sign below to indicate your consent.",
      "emphasis": false,
      "required": true
    }
  ],
  "signature": {
    "label": "Signature",
    "required": true,
    "dateLabel": "Date",
    "signatureLineText": "Participant Signature",
    "participantNameLabel": "Participant Name"
  }
}
```

## Usage Guide

### Creating a New Consent Form

1. **Access the Dashboard**
   - Navigate to `/admin/consent-creator`
   - Click "Create New Consent Form"

2. **Configure Basic Settings**
   - Enter form name (key auto-generated)
   - Set status (Draft/Active/Inactive)
   - Choose language

3. **Design the Header**
   - Set title and subtitle
   - Add organization name
   - Choose primary color

4. **Build Content Sections**
   - Drag section types from sidebar
   - Configure section properties
   - Reorder sections as needed

5. **Preview and Save**
   - Toggle preview to see final form
   - Save when satisfied with design

### Section Types Guide

#### Text Section
- **Purpose**: Display informational content
- **Configuration**: Title, content text, emphasis, required
- **Use Cases**: Instructions, disclaimers, explanations

#### List Section
- **Purpose**: Display bulleted lists
- **Configuration**: Title, list items (one per line), emphasis, required
- **Use Cases**: Terms and conditions, procedure steps, requirements

#### Signature Block
- **Purpose**: Collect participant signatures
- **Configuration**: Title, instruction text, emphasis, required
- **Features**: Participant name field, signature field, date field

### Management Operations

#### Editing Forms
- Click "Edit" on any form in the dashboard
- Modify sections, styling, or settings
- Save changes to update the form

#### Status Management
- **Draft**: Form is being developed, not available for use
- **Active**: Form is live and available for participants
- **Inactive**: Form is disabled but preserved

#### Preview Options
- **Live Preview**: Real-time preview in builder interface
- **Popup Preview**: Full-screen preview in new window
- **Keyboard Shortcut**: Ctrl/Cmd + P to toggle preview

## Security Features

### Authentication & Authorization
- **Admin Authentication**: Protected by `AdminAuthMiddleware`
- **Organization Isolation**: Users can only access their organization's forms
- **Session Management**: Secure session-based authentication

### Data Validation
- **JSON Validation**: Server-side JSON structure validation
- **Input Sanitization**: Client and server-side input cleaning
- **Error Handling**: Graceful error handling with user feedback

### Access Control
- **Route Protection**: All routes require admin authentication
- **Organization Filtering**: Database queries filtered by organization
- **Template Ownership**: Verification of template ownership before operations

## Integration

### Admin Dashboard
- Added "Consent Creator" card to admin dashboard
- Consistent styling with existing admin tools
- Direct navigation to consent creator interface

### Database Integration
- Uses existing `Template` model infrastructure
- Leverages `JsonWrapper` for JSONB storage
- Maintains consistency with assessment creator patterns

### Authentication Integration
- Uses `AuthController.userFromToken()` for user identification
- Integrates with existing admin authentication middleware
- Maintains session-based admin access control

## Error Messages
- **"Invalid JSON structure"**: Consent form JSON is malformed
- **"Access denied"**: User lacks admin permissions
- **"Template not found"**: Template ID is invalid or deleted
- **"User organization not found"**: User not properly associated with organization

## API Reference

### Template Creation
```http
POST /admin/consent-creator/create
Content-Type: application/json

{
  "name": "Research Consent Form",
  "key": "research_consent_form",
  "status": "draft",
  "language": "en",
  "templateJson": "{...consent form JSON...}"
}
```

### Template Update
```http
PUT /admin/consent-creator/:templateID
Content-Type: application/json

{
  "name": "Updated Consent Form",
  "key": "updated_consent_form",
  "status": "active",
  "language": "en",
  "templateJson": "{...updated consent form JSON...}"
}
```

### Template Deletion
```http
DELETE /admin/consent-creator/:templateID
```

## Development Notes

### Code Organization
- Controller follows same patterns as `AssessmentCreatorController`
- Templates use consistent styling with existing admin interfaces
- JavaScript follows modular organization with clear separation of concerns

### Performance Considerations
- JSON data stored efficiently in PostgreSQL JSONB fields
- Client-side preview updates optimized for smooth user experience
- Drag-and-drop operations use efficient DOM manipulation

### Future Enhancements
- Additional section types (checkboxes, radio buttons, file uploads)
- Advanced styling options (custom CSS, themes)
- Form analytics and completion tracking
- Integration with electronic signature services
- Multi-step consent forms with conditional logic
