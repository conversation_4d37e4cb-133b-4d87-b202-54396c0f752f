#!/usr/bin/env swift

import Foundation

// Demo script to show short URL functionality
print("🔗 Short URL System Demo")
print("========================")
print()

// Simulate the short URL generation process
func generateShortCode() -> String {
    let characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    return String((0..<6).map { _ in characters.randomElement()! })
}

// Demo the before and after URLs
let longToken = "XalGP59hnIA78tXwvnyjXcNOkPINvATggoym2RJHaXA="
let longUrl = "https://localhost:8080/password-reset/verify?token=\(longToken)"

print("❌ BEFORE (Long URL):")
print("   \(longUrl)")
print("   Length: \(longUrl.count) characters")
print()

let shortCode = generateShortCode()
let shortUrl = "https://localhost:8080/s/\(shortCode)"

print("✅ AFTER (Short URL):")
print("   \(shortUrl)")
print("   Length: \(shortUrl.count) characters")
print("   Reduction: \(longUrl.count - shortUrl.count) characters shorter!")
print()

print("📊 Short URL Benefits:")
print("   • \(Int((1.0 - Double(shortUrl.count) / Double(longUrl.count)) * 100))% shorter")
print("   • More user-friendly")
print("   • Easier to share via SMS")
print("   • Better for mobile displays")
print("   • Click tracking enabled")
print("   • Same security (30-min expiration)")
print()

print("🔧 Implementation Details:")
print("   • 6-character alphanumeric codes")
print("   • Collision detection & retry")
print("   • Database-backed with expiration")
print("   • CloudWatch security logging")
print("   • Analytics tracking")
print("   • Automatic cleanup")
print()

print("🚀 System is ready for testing!")
print("   Run the server and test: curl http://localhost:8080/password-reset/forgot")
