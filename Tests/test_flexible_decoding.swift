#!/usr/bin/env swift

import Foundation

// Copy the FlexibleStringOrNumber enum for testing
enum FlexibleStringOrNumber: Codable {
    case string(String)
    case number(Int)
    case double(Double)
    case bool(Bool)
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if let stringValue = try? container.decode(String.self) {
            self = .string(stringValue)
        } else if let intValue = try? container.decode(Int.self) {
            self = .number(intValue)
        } else if let doubleValue = try? container.decode(Double.self) {
            self = .double(doubleValue)
        } else if let boolValue = try? container.decode(Bool.self) {
            self = .bool(boolValue)
        } else {
            throw DecodingError.typeMismatch(FlexibleStringOrNumber.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Expected String, Int, Double, or Bool"))
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        switch self {
        case .string(let value):
            try container.encode(value)
        case .number(let value):
            try container.encode(value)
        case .double(let value):
            try container.encode(value)
        case .bool(let value):
            try container.encode(value)
        }
    }
    
    var stringValue: String {
        switch self {
        case .string(let value):
            return value
        case .number(let value):
            return String(value)
        case .double(let value):
            return String(value)
        case .bool(let value):
            return String(value)
        }
    }
}

struct TestEntity: Codable {
    let entityType: FlexibleStringOrNumber?
}

// Test JSON with number entityType
let jsonWithNumber = """
{
    "entityType": 1
}
"""

// Test JSON with string entityType
let jsonWithString = """
{
    "entityType": "category"
}
"""

print("Testing flexible decoding...")

// Test number decoding
if let data1 = jsonWithNumber.data(using: .utf8) {
    do {
        let entity1 = try JSONDecoder().decode(TestEntity.self, from: data1)
        print("✅ Successfully decoded number entityType: \(entity1.entityType?.stringValue ?? "nil")")
    } catch {
        print("❌ Failed to decode number entityType: \(error)")
    }
}

// Test string decoding
if let data2 = jsonWithString.data(using: .utf8) {
    do {
        let entity2 = try JSONDecoder().decode(TestEntity.self, from: data2)
        print("✅ Successfully decoded string entityType: \(entity2.entityType?.stringValue ?? "nil")")
    } catch {
        print("❌ Failed to decode string entityType: \(error)")
    }
}

print("Test completed!")
