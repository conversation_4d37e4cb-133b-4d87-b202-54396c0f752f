#!/bin/bash

# Background Check System Test Script
# Usage: ./test_background_check_system.sh [BASE_URL] [ADMIN_TOKEN] [MEMBER_ID]

set -e

# Configuration
BASE_URL=${1:-"http://localhost:8080"}
ADMIN_TOKEN=${2:-"your_admin_token_here"}
MEMBER_ID=${3:-"your_member_id_here"}

echo "🔐 Testing Background Check System"
echo "=================================="
echo "Base URL: $BASE_URL"
echo "Member ID: $MEMBER_ID"
echo ""

# Test 1: Trigger Background Check
echo "📋 Test 1: Triggering Background Check"
echo "--------------------------------------"
TRIGGER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/background-checks/members/$MEMBER_ID/trigger" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "John Test Doe",
    "zipCode": "12345"
  }')

echo "Response: $TRIGGER_RESPONSE"
echo ""

# Extract queue item ID if matches found
QUEUE_ID=$(echo "$TRIGGER_RESPONSE" | grep -o '"queueItemId":"[^"]*"' | cut -d'"' -f4 || echo "")

if [ -n "$QUEUE_ID" ]; then
    echo "✅ Background check triggered - Queue ID: $QUEUE_ID"
    
    # Test 2: Get Review Queue
    echo ""
    echo "📋 Test 2: Getting Review Queue"
    echo "-------------------------------"
    QUEUE_RESPONSE=$(curl -s -X GET "$BASE_URL/api/background-checks/queue" \
      -H "Authorization: Bearer $ADMIN_TOKEN")
    
    echo "Queue Response: $QUEUE_RESPONSE"
    echo ""
    
    # Test 3: Get Specific Queue Item
    echo "📋 Test 3: Getting Queue Item Details"
    echo "-------------------------------------"
    ITEM_RESPONSE=$(curl -s -X GET "$BASE_URL/api/background-checks/queue/$QUEUE_ID" \
      -H "Authorization: Bearer $ADMIN_TOKEN")
    
    echo "Item Response: $ITEM_RESPONSE"
    echo ""
    
    # Test 4: Approve Background Check
    echo "📋 Test 4: Approving Background Check"
    echo "-------------------------------------"
    APPROVE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/background-checks/queue/$QUEUE_ID/approve" \
      -H "Authorization: Bearer $ADMIN_TOKEN" \
      -H "Content-Type: application/json" \
      -d '{
        "notes": "Test approval - verified identity"
      }')
    
    echo "Approval Response: $APPROVE_RESPONSE"
    echo ""
    
    echo "✅ Background check approved successfully"
else
    echo "✅ Background check completed - No matches found"
fi

# Test 5: Check Member Security Profile
echo "📋 Test 5: Checking Member Security Profile"
echo "-------------------------------------------"
MEMBER_RESPONSE=$(curl -s -X GET "$BASE_URL/api/members/$MEMBER_ID" \
  -H "Authorization: Bearer $ADMIN_TOKEN")

echo "Member Response (Security Profile): $MEMBER_RESPONSE"
echo ""

# Test 6: Check Timeline Items
echo "📋 Test 6: Checking Timeline Items"
echo "----------------------------------"
TIMELINE_RESPONSE=$(curl -s -X GET "$BASE_URL/timeline?memberId=$MEMBER_ID" \
  -H "Authorization: Bearer $ADMIN_TOKEN")

echo "Timeline Response: $TIMELINE_RESPONSE"
echo ""

echo "🎉 Background Check System Test Complete!"
echo "========================================"
echo ""
echo "Next Steps:"
echo "1. Check the admin UI at: $BASE_URL/admin/background-checks/queue"
echo "2. Verify timeline items in member profile"
echo "3. Check that security tags were applied"
echo "4. Review audit logs for compliance"
