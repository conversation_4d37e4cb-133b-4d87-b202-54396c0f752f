#!/bin/bash

echo "Testing WHO ICD API Detailed Search..."
echo "======================================"

# Test the health endpoint first
echo "1. Testing Health Check..."
HEALTH_RESPONSE=$(curl -s -X GET "http://localhost:8080/whoicd/health")
echo "Health Response: $HEALTH_RESPONSE"

if [[ $HEALTH_RESPONSE == *"healthy"* ]]; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    exit 1
fi

echo ""

# Test the detailed search endpoint
echo "2. Testing WHO ICD Detailed Search..."
echo "Search Request: {
  \"query\": \"diabetes\",
  \"language\": \"en\"
}"

SEARCH_RESPONSE=$(curl -s -X POST "http://localhost:8080/whoicd/search/detailed" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{
    "query": "diabetes",
    "language": "en"
  }')

echo "Search Response: $SEARCH_RESPONSE"

if [[ $SEARCH_RESPONSE == *"error"* ]]; then
    echo "⚠️  Detailed search endpoint response: $SEARCH_RESPONSE"
else
    echo "✅ Detailed search endpoint working!"
    
    # Try to extract some key information
    if [[ $SEARCH_RESPONSE == *"results"* ]]; then
        echo "📊 Response contains results array"
    fi
    
    if [[ $SEARCH_RESPONSE == *"theCode"* ]]; then
        echo "📋 Response contains ICD codes"
    fi
    
    if [[ $SEARCH_RESPONSE == *"definition"* ]]; then
        echo "📖 Response contains definitions"
    fi
    
    if [[ $SEARCH_RESPONSE == *"processingTimeMs"* ]]; then
        echo "⏱️  Response includes processing time"
    fi
fi

echo ""
echo "Test completed!"
echo ""
echo "To test with real authentication:"
echo "1. Get a valid JWT token from your authentication endpoint"
echo "2. Replace 'test-token' in the script with your actual token"
echo "3. Run the script again"
echo ""
echo "Expected Response Structure:"
echo "{"
echo "  \"results\": ["
echo "    {"
echo "      \"id\": \"entity-id\","
echo "      \"title\": \"Condition name\","
echo "      \"score\": 0.95,"
echo "      \"theCode\": \"ICD-code\","
echo "      \"definition\": \"Medical definition\","
echo "      \"synonyms\": [\"synonym1\", \"synonym2\"],"
echo "      \"browserUrl\": \"https://icd.who.int/...\","
echo "      \"entityType\": \"0\","
echo "      \"important\": false,"
echo "      \"searchRank\": 1"
echo "    }"
echo "  ],"
echo "  \"totalResults\": 10,"
echo "  \"query\": \"diabetes\","
echo "  \"searchTimestamp\": \"2023-06-22T...\","
echo "  \"processingTimeMs\": 1250.5"
echo "}"
