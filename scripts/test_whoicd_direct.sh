#!/bin/bash

echo "Testing WHO ICD API directly..."
echo "================================"

# First get a token
echo "1. Getting WHO ICD access token..."
TOKEN_RESPONSE=$(curl -s -X POST "https://icdaccessmanagement.who.int/connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=1d7b63c0-9c0b-4852-b756-46219fd7f588_e2c51b17-9a8f-4bce-810c-640268b6c56b&client_secret=yyDt5vyvgSiSNwOlts2/NFvXl0TqpiLxgpiE8WVDmks=&scope=icdapi_access&grant_type=client_credentials")

echo "Token Response: $TOKEN_RESPONSE"

# Extract access token
ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
    echo "❌ Failed to get access token"
    exit 1
fi

echo "✅ Got access token: ${ACCESS_TOKEN:0:20}..."
echo ""

# Now make a search request
echo "2. Making search request to WHO ICD API..."
SEARCH_RESPONSE=$(curl -s -X GET "https://id.who.int/icd/release/11/2024-01/mms/search?q=diabetes&subtreeFilterUsesFoundationDescendants=false&includeKeywordResult=true&useFlexisearch=true&flatResults=true&highlightingEnabled=true&medicalCodingMode=true" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Accept: application/json" \
  -H "API-Version: v2" \
  -H "Accept-Language: en")

echo "Search Response:"
echo "$SEARCH_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$SEARCH_RESPONSE"

echo ""
echo "Test completed!"
