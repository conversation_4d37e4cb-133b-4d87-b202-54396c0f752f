#!/bin/bash

echo "Testing WHO ICD API Detailed Search with Simple Path..."
echo "======================================================"

# First get a real WHO ICD token
echo "1. Getting WHO ICD access token..."
TOKEN_RESPONSE=$(curl -s -X POST "https://icdaccessmanagement.who.int/connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=1d7b63c0-9c0b-4852-b756-46219fd7f588_e2c51b17-9a8f-4bce-810c-640268b6c56b&client_secret=yyDt5vyvgSiSNwOlts2/NFvXl0TqpiLxgpiE8WVDmks=&scope=icdapi_access&grant_type=client_credentials")

# Extract access token
ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
    echo "❌ Failed to get access token"
    echo "Token Response: $TOKEN_RESPONSE"
    exit 1
fi

echo "✅ Got access token: ${ACCESS_TOKEN:0:20}..."
echo ""

# Test the detailed search endpoint with simple path
echo "2. Testing WHO ICD Detailed Search at /whoicd/detailed..."
SEARCH_RESPONSE=$(curl -s -X POST "http://localhost:8080/whoicd/detailed" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d '{
    "query": "diabetes",
    "language": "en"
  }')

echo "Search Response:"
echo "$SEARCH_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$SEARCH_RESPONSE"

if [[ $SEARCH_RESPONSE == *"error"* ]]; then
    echo ""
    echo "⚠️  Detailed search failed: $SEARCH_RESPONSE"
else
    echo ""
    echo "✅ Detailed search endpoint working!"
fi

echo ""
echo "Test completed!"
