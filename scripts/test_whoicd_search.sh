#!/bin/bash

# Test script for WHO ICD API search functionality
# This script tests the WHO ICD API integration with a simple search

echo "Testing WHO ICD API Search..."
echo "================================"

# Test 1: Health Check
echo "1. Testing Health Check..."
HEALTH_RESPONSE=$(curl -s -X GET "http://localhost:8080/whoicd/health")
echo "Health Response: $HEALTH_RESPONSE"

if [[ $HEALTH_RESPONSE == *"healthy"* ]]; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    exit 1
fi

echo ""

# Test 2: Search for diabetes (requires authentication)
echo "2. Testing WHO ICD Search (Note: This will fail without valid JWT token)..."

# Create a test search request
SEARCH_REQUEST='{
  "query": "diabetes",
  "language": "en"
}'

echo "Search Request: $SEARCH_REQUEST"

# Make the search request (this will fail with 401 without proper JWT token)
SEARCH_RESPONSE=$(curl -s -X POST "http://localhost:8080/whoicd/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d "$SEARCH_REQUEST")

echo "Search Response: $SEARCH_RESPONSE"

if [[ $SEARCH_RESPONSE == *"Unauthorized"* ]]; then
    echo "✅ Search endpoint properly requires authentication"
else
    echo "⚠️  Search endpoint response: $SEARCH_RESPONSE"
fi

echo ""
echo "Test completed!"
echo ""
echo "To test with real authentication:"
echo "1. Get a valid JWT token from your authentication endpoint"
echo "2. Replace 'test-token' in the script with your actual token"
echo "3. Run the script again"
