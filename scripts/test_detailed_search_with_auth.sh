#!/bin/bash

echo "Testing WHO ICD API Detailed Search with Real Authentication..."
echo "=============================================================="

# First get a real WHO ICD token
echo "1. Getting WHO ICD access token..."
TOKEN_RESPONSE=$(curl -s -X POST "https://icdaccessmanagement.who.int/connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=1d7b63c0-9c0b-4852-b756-46219fd7f588_e2c51b17-9a8f-4bce-810c-640268b6c56b&client_secret=yyDt5vyvgSiSNwOlts2/NFvXl0TqpiLxgpiE8WVDmks=&scope=icdapi_access&grant_type=client_credentials")

# Extract access token
ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
    echo "❌ Failed to get access token"
    echo "Token Response: $TOKEN_RESPONSE"
    exit 1
fi

echo "✅ Got access token: ${ACCESS_TOKEN:0:20}..."
echo ""

# Test the health endpoint first
echo "2. Testing Health Check..."
HEALTH_RESPONSE=$(curl -s -X GET "http://localhost:8080/whoicd/health")
echo "Health Response: $HEALTH_RESPONSE"

if [[ $HEALTH_RESPONSE == *"healthy"* ]]; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    exit 1
fi

echo ""

# Test the detailed search endpoint with real authentication
echo "3. Testing WHO ICD Detailed Search with Real Token..."
echo "Search Request: {
  \"query\": \"diabetes\",
  \"language\": \"en\"
}"

SEARCH_RESPONSE=$(curl -s -X POST "http://localhost:8080/whoicd/search/detailed" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d '{
    "query": "diabetes",
    "language": "en"
  }')

echo "Search Response:"
echo "$SEARCH_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$SEARCH_RESPONSE"

if [[ $SEARCH_RESPONSE == *"error"* ]]; then
    echo ""
    echo "⚠️  Detailed search failed: $SEARCH_RESPONSE"
else
    echo ""
    echo "✅ Detailed search endpoint working!"
    
    # Try to extract some key information
    if [[ $SEARCH_RESPONSE == *"results"* ]]; then
        echo "📊 Response contains results array"
    fi
    
    if [[ $SEARCH_RESPONSE == *"theCode"* ]]; then
        echo "📋 Response contains ICD codes"
    fi
    
    if [[ $SEARCH_RESPONSE == *"definition"* ]]; then
        echo "📖 Response contains definitions"
    fi
    
    if [[ $SEARCH_RESPONSE == *"processingTimeMs"* ]]; then
        echo "⏱️  Response includes processing time"
    fi
    
    # Count results
    RESULT_COUNT=$(echo "$SEARCH_RESPONSE" | grep -o '"searchRank"' | wc -l)
    echo "📈 Found $RESULT_COUNT detailed results"
fi

echo ""
echo "Test completed!"
