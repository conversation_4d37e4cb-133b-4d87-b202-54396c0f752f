#!/bin/bash

# Test script for Intervention AutoAssignTask feature
# This script tests the new intervention creation with auto-task assignment

echo "🧪 Testing Intervention AutoAssignTask Feature"
echo "=============================================="

# Set the base URL
BASE_URL="http://localhost:8080"

# Test data
MEMBER_ID="123e4567-e89b-12d3-a456-426614174000"  # Replace with actual member ID
CARE_PLAN_ID="123e4567-e89b-12d3-a456-426614174001"  # Replace with actual care plan ID

echo ""
echo "📋 Test 1: Create Intervention with autoAssignTask=true"
echo "------------------------------------------------------"

curl -X POST "${BASE_URL}/api/careplans/${CARE_PLAN_ID}/interventions" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Medication Management Intervention",
    "action": "Schedule weekly medication review",
    "responsibleParty": "Primary Care Nurse",
    "responsiblePartyId": "nurse-001",
    "status": "active",
    "dueDate": "2025-02-01T00:00:00Z",
    "autoAssignTask": true
  }' | jq '.'

echo ""
echo "📋 Test 2: Create Intervention with autoAssignTask=false"
echo "-------------------------------------------------------"

curl -X POST "${BASE_URL}/api/careplans/${CARE_PLAN_ID}/interventions" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Follow-up Intervention",
    "action": "Schedule follow-up appointment",
    "responsibleParty": "Care Coordinator",
    "responsiblePartyId": "coordinator-002",
    "status": "pending",
    "dueDate": "2025-02-15T00:00:00Z",
    "autoAssignTask": false
  }' | jq '.'

echo ""
echo "📋 Test 3: Create Intervention without autoAssignTask (default behavior)"
echo "-----------------------------------------------------------------------"

curl -X POST "${BASE_URL}/api/careplans/${CARE_PLAN_ID}/interventions" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Basic Intervention",
    "action": "Conduct health assessment",
    "responsibleParty": "Health Assessor",
    "responsiblePartyId": "assessor-003",
    "status": "active",
    "dueDate": "2025-03-01T00:00:00Z"
  }' | jq '.'

echo ""
echo "📋 Test 4: List all interventions for the care plan"
echo "---------------------------------------------------"

curl -X GET "${BASE_URL}/api/careplans/${CARE_PLAN_ID}/interventions" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "✅ Testing completed!"
echo ""
echo "📝 Notes:"
echo "- Replace MEMBER_ID and CARE_PLAN_ID with actual values"
echo "- Ensure the server is running on localhost:8080"
echo "- Check server logs for task creation messages"
echo "- Verify tasks were created in the tasks table for autoAssignTask=true cases"
