import Fluent

struct FixAcuityScoreReferences: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        // Drop and recreate the references column with correct JSONB type
        return database.schema(AcuityScore.schema)
            .deleteField("references")
            .update()
            .flatMap { _ in
                database.schema(AcuityScore.schema)
                    .field("references", .custom("JSONB"), .required)
                    .update()
            }
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        // Revert back to JSON type
        return database.schema(AcuityScore.schema)
            .deleteField("references")
            .update()
            .flatMap { _ in
                database.schema(AcuityScore.schema)
                    .field("references", .json, .required)
                    .update()
            }
    }
}
