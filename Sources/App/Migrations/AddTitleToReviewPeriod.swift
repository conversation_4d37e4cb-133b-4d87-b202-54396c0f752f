//
//  AddTitleToReviewPeriod.swift
//  
//
//  Created by Augment Agent on 9/23/25.
//

import Foundation
import Fluent
import Vapor

struct AddTitleToReviewPeriod: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("review_periods")
            .field("title", .string)
            .update()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("review_periods")
            .deleteField("title")
            .update()
    }
}
