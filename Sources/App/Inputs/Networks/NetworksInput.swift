//
//  File.swift
//  
//
//  Created by <PERSON> on 2/4/23.
//

import Foundation
import Vapor
import Fluent

struct NetworkCreateInput: Content {
    var orgID:          String?
    var name:           String?
    var types:         [String]?
    var status:         String?
    var contact:        String?
    var website:        String?
    var memberBook:     Bool?
    var desc:           String?
    
    var address:        AddressInput?
    
    var phone:          PhoneInput?
    
    var services:       [String]?
    
    var carriers:       [String]?
    
    var invites:        [String]?
        
    var hasInvites: Bool {
        return !(invites?.isEmpty ?? true)
    }
        
    func returnUpdatedModel(network:Network) -> Network {
        if let name = name {
            network.name = name
        }
        if let type = types {
            network.types = type
        }
        if let status = status {
            network.status = status
        }
        if let contact = contact {
            network.contact = contact
        }
        if let website = website {
            network.website = website
        }
        
        if let memberBook = memberBook {
            network.memberBook = memberBook
        }
        
        return network
    }
    
    func network() -> Network {
        return Network(name: name ?? "",
                       types: types ?? [],
                       status: status ?? "",
                       contact: contact ?? "",
                       website: website ?? "",
                       memberBook: false)
    }
    
    var schedulerInput: OrganizationQlCreateInput {
        var input = OrganizationQlCreateInput(name: name ?? "",
              parentId: schedulerParentOrg,
              desc: desc ?? "",
              kind: "facility",
              phones: [],
              addresses: [])
        
        if let address {
            input.addresses.append(address)
        }
        
        if let phone {
            input.phones.append(phone)
        }
        
        return input
    }
}

