//
//  File.swift
//  hmbl-core
//
//  Created by <PERSON> on 3/21/25.
//

import Foundation
import Vapor

let schedulerParentOrg = isProduction ? "1" : "1"

struct OrgGraphQl: Codable, Content {
    let id: String
    let name: String?
    let desc: String?
    let addresses: [AddressGraphQl]?
    let phones: [PhonesGraphQl]?
    let services: [ServiceGraphQl]?
    let providers: ProviderResponse?
    
    struct ProviderResponse: Content, Codable {
        let nodes: [Provider]
        
        struct Provider: Content, Codable {
            let id: String
            let fullName: String
        }
    }
}

struct ServiceGraphQl: Codable {
    let id: String?
    let kind: String?
    let durationMins: Int?    
    let title: String?
    let desc: String?
    let nextAvailableAppt: [AppointmentGraphQl]?
}


struct SchedulerResponse: Content, Codable {
    let data:OrgGraphQlResponse
    
    struct OrgGraphQlResponse: Content, Codable {
        let org: OrgGraphQl
    }
}

struct AppointmentGraphqlResponse: Content, Codable {
    let data:AppointmentServiceResponse
    
    struct AppointmentServiceResponse: Content, Codable {
        let org: OrgGraphQl
        
        struct OrgGraphQl: Codable, Content {
            let id: String
            let service: AppointmentGraphQl?
            let providers: ProviderResponse?
            
            struct ProviderResponse: Content, Codable {
                let nodes: [Provider]
                
                struct Provider: Content, Codable {
                    let id: String
                    let fullName: String
                }
            }
        }
        
        struct AppointmentGraphQl: Codable {
            let id: String
            let title: String
            let durationMins: Int
            let availableTimesIso: [String]?
        }
    }
}

struct OrgGraphQlResponse: Content, Codable {
    let org: ChildrenGraphQl
    
    struct ChildrenGraphQl: Codable {
        let children: [OrgGraphQl]
    }
    
    struct OrgGraphQl: Codable {
        let id: String
        let name: String
        let addresses: [AddressGraphQl]
        let services: [ServiceGraphQl]
    }

    struct ServiceGraphQl: Codable {
        let id: String
        let kind: String
        let durationMins: Int
        let title: String
        let nextAvailableAppt: [AppointmentGraphQl]?
    }
}

struct AppointmentGraphQl: Codable {
    let id: String?
    let startIso: String
    let startEpoch: Int?
    let endIso: String?
    let endEpoch: Int?
}

struct AddressGraphQl: Codable {
    let address: String
}

struct PhonesGraphQl: Codable {
    let number: String
}

struct OrgCreateResponseData: Content, Codable {
    let data: OrgCreateResponse
    
    struct OrgCreateResponse: Content, Codable {
        let orgCreate: OrgResponseData
        
        struct OrgResponseData: Content, Codable {
            let success: Bool
            let errors: [GraphQLError]?
            let organization: OrganizationData?
        }
        
        struct OrganizationData: Codable {
            let id: String
            let name: String
        }
    }
}


struct CancelAptResponseData: Content, Codable {
    let data: ApptCancelResponse
    
    struct ApptCancelResponse: Content, Codable {
        let apptCancel: ApptCancelResponseData
        
        struct ApptCancelResponseData: Content, Codable {
            let success: Bool
            let errors: [GraphQLError]?
            let result: ResultData?
        }
        
        struct ResultData: Codable {
            let id: String
        }
    }
}

struct UserFindOrCreateResponse: Content, Codable {
    let data: UserFindOrCreate
    
    struct UserFindOrCreate: Content, Codable {
        let userFindOrCreate: UserGraphQlResponse
    }
    
    struct UserGraphQlResponse: Content, Codable {
        let success: Bool
        let errors: [GraphQLError]?
        let user: UserResponse
    }
    
    struct UserResponse: Content, Codable {
        let id: String
    }
}



struct ServiceResponseData: Content, Codable {
    let serviceCreate: ServiceResponseItem
    
    struct ServiceResponseItem: Content, Codable {
        let success: Bool
        let errors: [GraphQLError]?
        let service: ServiceData?
    }
    
    struct ServiceData: Codable {
        let id: String
        let organization: ServiceOrganizationData
    }
    
    struct ServiceOrganizationData: Codable {
        let id: String
    }
}

struct ServiceGraphQlCreateInput: Content {
   var org: String
   var title: String
   var kind: String
   var desc: String
   var addresses: [AddressInput]
}

struct OrganizationQlCreateInput: Content {
    var name: String
    var parentId: String
    var desc: String
    var kind: String
    var phones: [PhoneInput]
    var addresses: [AddressInput]
}

struct CreateOrgMutation: Content {
    let query: String
    let variables: [String: OrganizationQlCreateInput]?
    
    init(input: OrganizationQlCreateInput) {
        self.query = """
        mutation CreateOrg($input: OrganizationCreateInput!) {
            orgCreate(input: $input) {
                errors {
                    message
                }
                success
                organization {
                    id
                    name
                }
            }
        }
        """
        self.variables = ["input": input]
    }
}

struct CancelAptMutation: Content {
    let query: String
    let variables: [String: IDInput]?
    
    init(input: IDInput) {
        self.query = """
        mutation ApptCancel($input: IDInput!) {
          apptCancel(input: $input) {
            success
            errors {
              message
            }
            result {
              id
            }
          }
        }
        """
        self.variables = ["input": input]
    }
}



struct AppointmentCreateInput: Content {
    var endEpoch: Int
    var kind: String
    var org: String
    var person: String
    var service: String
    var reason: String?
    var provider: String?
    var startEpoch: Int        
}

struct UserFindOrCreateInput: Content {
    var dob: String
    var firstName: String
    var lastName: String
    var org: String
    var role: String
}

struct UserFindOrCreateMutation: Content {
    let query: String
    let variables: [String: UserFindOrCreateInput]?

    init(input: UserFindOrCreateInput) {
        self.query = """
        mutation UserFindOrCreate($input: UserFindOrCreateInput!) {
            userFindOrCreate(input: $input) {
                success
                errors {
                  message
                }
                user {
                  id
                }
            }
        }
        """
        self.variables = ["input": input]
    }
}


struct IDInput: Content {
    var id: String
}


struct AppointmentBookMutation: Content {
    let query: String
    let variables: [String: IDInput]?

    init(input: IDInput) {
        self.query = """
        mutation AppointmentBook($input: IDInput!) {
            apptBook(input: $input) {
                success
                errors {
                  message
                }
                appointment {
                  id
                }
            }
        }
        """
        self.variables = ["input": input]
    }
}


struct AppointmentCreateResponse: Content {
    
    let data:AppCreateResposne
    
    struct AppCreateResposne: Content, Codable {
        let apptCreate: AppointmentResposne
    }
    
    struct AppointmentResposne: Content, Codable {
        let success: Bool
        let errors: [GraphQLError]?
        let appointment: Appointment?
    }
    
    struct Appointment: Content, Codable {
        let id: String
        let actualDurationMins: Int?
        let service: ServiceGraphQl?
    }
    
}

struct AppointmentCreateMutation: Content {
    let query: String
    let variables: [String: AppointmentCreateInput]?

    init(input: AppointmentCreateInput) {
        self.query = """
        mutation ApptCreate($input: AppointmentCreateInput!) {
            apptCreate(input: $input) {
                success
                errors {
                  message
                }
                appointment {
                  id
                  actualDurationMins
                  service {
                    title
                  }
                }
            }
        }
        """
        self.variables = ["input": input]
    }
}



struct ProviderTimeInput: Content {
    var org: String// "4",
    var service: String// "68",
    var date: String// "03/27/2025"
    var provider: String?
}

struct AppointmentProviderTimesQuery: Content {
    let query: String
    let org: String
    let service: String
    let provider: String?
    let date: String
    let variables: [String: String]?
    
    init(org: String, service: String, provider: String? = nil, date: String) {
        self.org = org
        self.service = service
        self.provider = provider
        self.date = date
        self.query = """
            query AppointmentProviderTimesQuery($org: ID, $service: ID, $provider: ID, $date: String) {
            org(id: $org) {
                id
                service(id: $service) {
                  id
                  title
                  durationMins
                  availableTimesIso(date: $date, providerId: $provider)                  
                }
                providers {
                    nodes {
                        id
                        fullName
                    }
                }            
              }
            }
        }
        """
        var data  = ["org": org, "service": service, "date": date]
        if let provider, !provider.isEmpty {
            data["provider"] = provider
        }
        self.variables = data
    }
}

struct GraphQLQuery: Content {
    let query: String
}


struct FetchServicesQuery: Content {
    let query: String
    let variables: [String: String]?

    init(id: String) {
        self.query = """
        query FetchServicesQuery($id: ID) {
            org(id: $id) {
                id
                name
                addresses {
                    address                        
                }                    
                services {
                    id
                    title
                    kind
                    durationMins
                    nextAvailableAppt {
                        startIso
                        startEpoch
                        endIso
                        endEpoch
                    }
                }
                providers {
                    nodes {
                        id
                        fullName
                    }
                }
            }
        }
        """
        self.variables = ["id": id]  // Pass `id` dynamically as a variable
    }
}

struct CreateServiceMutation: Content {
    let query: String
    let variables: [String: ServiceGraphQlCreateInput]?

    init(input: ServiceGraphQlCreateInput) {
        self.query = """
        mutation CreateService($input: ServiceCreateInput!) {
            serviceCreate(input: $input) {
                errors {
                    message
                }
                success
                service {
                    organization {
                        id
                    }
                    id
                }
            }
        }
        """
        self.variables = ["input": input]
    }
}


struct NextAppointmentMutation: Content {
    static let query = """
query NextAvailableAppointment {
    org(id: \(schedulerParentOrg) {
      children {
      name
      addresses {
        address
      }
      services {
        id
        kind
        durationMins
        title
        nextAvailableAppt {
          startIso
          startEpoch
          endIso
          endEpoch
         }
        }
      }
    }
  }
"""
}


//MARK: - Connected Network
struct FetchConnectedNetworkQuery: Content {
    let query: String
    let variables: [String: String]?
    
    init(id: String) {
        self.query = """
          query FetchConnectedNetworkQuery($id: ID!) {
            org(id: $id) {
              id
              children {
                id
                name
                desc
                addresses {
                  address                  
                }
                phones {
                  number
                }
              }
            }
          }
        """
        self.variables = ["id": id]
    }
}

struct NextAppointmentQuery: Content {
    let query: String
    let variables: [String: String]?
    
    init(id: String) {
        self.query = """
          query NextAvailableAppointment($id: ID!) {
            org(id: $id) {
                id
                name
                desc
                addresses {
                  address
                }
                phones {
                  number
                }
                services {
                  id
                  kind
                  durationMins
                  title
                  desc
                  nextAvailableAppt {
                    startIso
                  }
                }
              }
          }
        """
        self.variables = ["id": id]
    }
}



struct ConnectedNetworkResponse: Content, Codable {
    let data:OrgGraphQlResponse
    
    struct OrgGraphQlResponse: Content, Codable {
        let org: OrgChildren
    }
    
    struct OrgChildren: Codable, Content {
        let children: [OrgGraphQl]
    }
}

struct NextApptNetworkResponse: Content, Codable {
    let data:OrgGraphQlResponse
    
    struct OrgGraphQlResponse: Content, Codable {
        let org: OrgGraphQl
    }
}

struct ConnectedAppointmentProviderTimesQuery: Content {
    let operationName: String
    let query: String
    let variables: [String: String]?
    
    init(org: String, service: String, date: String, provider: String? = nil) {
        self.operationName = "AppointmentProviderTimesQuery"
        self.query = """
          query AppointmentProviderTimesQuery($org: ID, $service: ID, $provider: ID, $date: String) {
            org(id: $org) {
              id
              service(id: $service) {
                id
                title
                availableTimesIso(date: $date, providerId: $provider)               
              }              
            }
          }
        """
        var data  = [
            "org": org,
            "service": service,
            "date": date
        ]
        if let provider, !provider.isEmpty {
            data["provider"] = provider
        }
        self.variables = data
    }
}

struct AppointmentTimesResponse: Content, Codable {
    let data: AppointmentTimesData
    
    struct AppointmentTimesData: Codable {
        let org: OrgWithService
    }

    struct OrgWithService: Codable {
        let id: String
        let service: ServiceWithTimes
        
        enum CodingKeys: String, CodingKey {
            case id, service
        }
    }

    struct ServiceWithTimes: Codable {
        let id: String
        let title: String
        let availableTimesIso: [String]  // Array of ISO time strings
        
        enum CodingKeys: String, CodingKey {
            case id, title, availableTimesIso
        }
    }
}


struct ServicesAndProvidersQuery: Content {
    let operationName: String
    let query: String
    let variables: [String: String]
    
    init(id: String) {
        self.operationName = "ServicesAndProvidersQuery"
        self.query = """
          query ServicesAndProvidersQuery($id: ID) {
            org(id: $id) {
              id
              name
              services {
                id
                durationMins
                kind
                status
                title                
              }
              providers {
                nodes {
                  id
                  firstName
                  lastName                 
                }               
              }              
            }
          }
        """
        self.variables = ["id": id]
    }
}


struct ProvidersAndServicesResponse:  Content, Codable {
    let data: AppointmentCreateData
    
    struct AppointmentCreateData: Content, Codable {
        let org: OrgWithServicesAndProviders
    }
}


struct OrgWithServicesAndProviders: Codable {
    let id: String?
    let name: String?
    let services: [ServiceGraphQl]?
    let providers: ProviderConnection?
    
    enum CodingKeys: String, CodingKey {
        case id, name, services, providers
    }
    
    struct Service: Codable {
        let id: String?
        let durationMins: Int?
        let kind: String?
        let status: String?
        let title: String?
        
        enum CodingKeys: String, CodingKey {
            case id, durationMins, kind, status, title
        }
    }

    struct ProviderConnection: Codable {
        let nodes: [Provider]
        
        enum CodingKeys: String, CodingKey {
            case nodes
        }
    }

    struct Provider: Codable {
        let id: String?
        let firstName: String?
        let lastName: String?
        
        enum CodingKeys: String, CodingKey {
            case id, firstName, lastName
        }
    }
}
