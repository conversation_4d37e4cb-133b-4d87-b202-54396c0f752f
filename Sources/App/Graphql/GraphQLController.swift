//
//  graphql_controller.swift
//  hmbl-core
//
//  Created by <PERSON> on 3/21/25.
//
import Vapor
import Foundation

struct GraphQLController {
    
    static func kioskLogin(req: Request) throws -> EventLoopFuture<LoginGraphQlResponse> {
        let service = GraphQLService(client: req.client)
        let variables = [
            "email": AnyCodable("<EMAIL>"),
            "password": AnyCodable("Welcome2023!")
        ]
        return service.sendRequest(query: LoginMutation.query, variables: variables, responseType: LoginGraphQlResponse.self)
    }
    
    static func fetchNextAvailableAppointment(token: String, req: Request) throws -> EventLoopFuture<OrgGraphQlResponse> {
        let service = GraphQLService(client: req.client, token: token)
        return service.sendRequest(query: NextAppointmentMutation.query, responseType: OrgGraphQlResponse.self)
    }
    
    static func fetchAppointmentTimes(req: Request, input: ProviderTimeInput) throws -> EventLoopFuture<AppointmentGraphqlResponse> {
        return try GraphQLController.kioskLogin(req: req).flatMap { response in
            let token  = response.login.user?.token ?? ""
            let service = GraphQLService(client: req.client, token: token)
            let graphql = AppointmentProviderTimesQuery(org: input.org,
                                                        service: input.service,
                                                        provider: input.provider,
                                                        date: input.date)
            return try! service.create(service: req, input: graphql, token: token).flatMap { response in
                response.decode(AppointmentGraphqlResponse.self, on: req.eventLoop)
            }
        }
    }
    
    static func cancelApt(req: Request, id: String) throws -> EventLoopFuture<CancelAptResponseData> {
        return try GraphQLController.kioskLogin(req: req).flatMap { response in
            let token  = response.login.user?.token ?? ""
            let service = GraphQLService(client: req.client, token: token)
            let mutation = CancelAptMutation(input: .init(id: id))
            return try! service.create(service: req, input: mutation, token: token)
                .flatMap { response in
                response.decode(CancelAptResponseData.self, on: req.eventLoop)
            }
        }
    }
    
    static func fetchScheduler(req: Request, id: String) throws -> EventLoopFuture<SchedulerResponse> {
        return try GraphQLController.kioskLogin(req: req).flatMap { response in
            let token  = response.login.user?.token ?? ""
            let service = GraphQLService(client: req.client, token: token)
            return try! service.create(service: req, input: FetchServicesQuery(id: id), token: token).flatMap { response in
                response.decode(SchedulerResponse.self, on: req.eventLoop)
            }
        }
    }

    static func createOrg(token: String,
                          req: Request,
                          input: OrganizationQlCreateInput,
                          network: Network,
                          emails: [String]) throws -> EventLoopFuture<Network> {
        let service = GraphQLService(client: req.client, token: token)
        let mutation = CreateOrgMutation(input: input)
        return try service.create(service: req, input: mutation, token: token).flatMap { response in
            response.decode(OrgCreateResponseData.self, on: req.eventLoop)
        }
        .flatMap { responseData in
            let orgName = responseData.data.orgCreate.organization?.name.capitalized ?? ""
            let orgId = responseData.data.orgCreate.organization?.id ?? ""
            network.schedulerId = orgId
            return network.update(on: req.db).flatMap { _ in
                return GraphQLController.sendInvites(req: req,
                                                     emails: emails,
                                                     orgName: orgName,
                                                     orgId: orgId).transform(to: network)
            }
        }
    }
    
    static func sendInvites(req: Request, emails: [String], orgName: String, orgId: String) -> EventLoopFuture<Void> {
        let futures = emails.map { email in
            do {
                return try ContactController.emailScheduler(req: req, invite: .init(
                    first: "",
                    email: email,
                    org: orgName,
                    url: "https://wellup-web-stg-38c16252b3d5.herokuapp.com/invite?role=admin&org=\(orgId)&email=\(email)",
                    tempId: EmailTempaltes.scheduler.tempalte()
                ))
            } catch {
                return req.eventLoop.makeFailedFuture(error) // Ensure errors don’t crash the app
            }
        }
        
        return req.eventLoop.flatten(futures).transform(to: ())
    }
    
    static func createAppointment(req: Request, input: AppointmentCreateInput, userFindOrCreate: Member? = nil) throws -> EventLoopFuture<ClientResponse> {
        return try GraphQLController.kioskLogin(req: req).flatMap { kioskResponse in
            let token  = kioskResponse.login.user?.token ?? ""
            let service = GraphQLService(client: req.client, token: token)
            if let member = userFindOrCreate {
                return try! GraphQLController.userFindOrCreate(req: req, org: input.org, member: member, token: token, service: service).flatMap({ findResponse in
                    if var json = member.meta {
                        json.schedulerMemberId = findResponse.data.userFindOrCreate.user.id
                        member.meta = json
                    } else {
                        member.meta = .init(data: [:], schedulerMemberId: findResponse.data.userFindOrCreate.user.id)
                    }
                    return member.update(on: req.db).flatMap { _ in
                        var updateInput = input
                        updateInput.person = findResponse.data.userFindOrCreate.user.id
                        let mutation = AppointmentCreateMutation(input: updateInput)
                        
                        return try! service.create(service: req, input: mutation, token: token).flatMap { response in
                            response.decode(AppointmentCreateResponse.self, on: req.eventLoop)
                        }.flatMap({ apptCreateResponse in
                            
                            let aptId = apptCreateResponse.data.apptCreate.appointment?.id ?? ""
                            let duration = apptCreateResponse.data.apptCreate.appointment?.actualDurationMins ?? 60
                            let serviceName = apptCreateResponse.data.apptCreate.appointment?.service?.title ?? input.service
                            
                            return try! GraphQLController.bookAppointment(req: req,
                                                                          input: .init(id: apptCreateResponse.data.apptCreate.appointment?.id ?? ""),
                                                                          token: token,
                                                                          service: service).flatMap({ bookResponse in
                                return try! AuthController.userIdFromToken(req: req).flatMap { userId in
                                    let model = Appointment(creatorID: userId.uuidString,
                                                            memberID: input.person,
                                                            title: serviceName,
                                                            status: "booked",
                                                            kind: input.kind,
                                                            desc: input.reason ?? "",
                                                            scheduleEpoc: input.startEpoch,
                                                            timeZone: nil,
                                                            duration: "\(duration)",
                                                            memberBook: false,
                                                            aptId: aptId)
                                    return model.create(on: req.db).flatMap { _ in
                                        return req.eventLoop.future(bookResponse)
                                    }
                                }
                            })
                        })
                    }
                })
            } else {
                let mutation = AppointmentCreateMutation(input: input)
                return try! service.create(service: req, input: mutation, token: token).flatMap { response in
                    response.decode(AppointmentCreateResponse.self, on: req.eventLoop)
                }.flatMap({ response in
                    
                    let aptId = response.data.apptCreate.appointment?.id ?? ""
                    let duration = response.data.apptCreate.appointment?.actualDurationMins ?? 60
                    let serviceName = response.data.apptCreate.appointment?.service?.title ?? input.service
                    
                    return try! GraphQLController.bookAppointment(req: req,
                                                                  input: .init(id: aptId),
                                                                  token: token,
                                                                  service: service).flatMap({ bookResponse in
                        return try! AuthController.userIdFromToken(req: req).flatMap { userId in
                            let model = Appointment(creatorID: userId.uuidString,
                                                    memberID: input.person,
                                                    title: serviceName,
                                                    status: "booked",
                                                    kind: input.kind,
                                                    desc: input.reason ?? "",
                                                    scheduleEpoc: input.startEpoch,
                                                    timeZone: nil,
                                                    duration: "\(duration)",
                                                    memberBook: false,
                                                    aptId: aptId)
                            return model.create(on: req.db).flatMap { _ in
                                return req.eventLoop.future(bookResponse)
                            }
                        }
                    })
                })
            }
        }
    }
    
    static func bookAppointment(req: Request, input: IDInput, token: String, service: GraphQLService) throws -> EventLoopFuture<ClientResponse> {
        let mutation = AppointmentBookMutation(input: input)
        return try! service.create(service: req, input: mutation, token: token)
    }
    
    static func userFindOrCreate(req: Request, org: String, member: Member, token: String, service: GraphQLService) throws -> EventLoopFuture<UserFindOrCreateResponse> {
        let firstName = member.firstName
        let lastName = member.lastName
        let dob = member.dob
        let mutation = UserFindOrCreateMutation(input: .init(dob: dob, firstName: firstName, lastName: lastName, org: org,role: "person"))
        return try! service.create(service: req, input: mutation, token: token).flatMap({ response in
            response.decode(UserFindOrCreateResponse.self, on: req.eventLoop)
        })
    }
    
    static func createService(token: String, req: Request) throws -> EventLoopFuture<ClientResponse> {
        let service = GraphQLService(client: req.client, token: token)
        let serviceInput = ServiceGraphQlCreateInput(
            org: "1",
            title: "Health Service 3",
            kind: "appointment",
            desc: "Providing essential medical services.",
            addresses: [
                AddressInput(
                    street: "1234 Main Street",
                    street2: "",
                    city: "Coral Gables",
                    state: "FL",
                    zip: "33143",
                    label: "main"
                )
            ]
        )
        let mutation = CreateServiceMutation(input: serviceInput)
        return try service.create(service: req, input: mutation, token: token)
    }
}



struct AnyCodable: Codable {
    let value: Any

    init<T>(_ value: T) {
        self.value = value
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        switch value {
        case let val as String:
            try container.encode(val)
        case let val as Int:
            try container.encode(val)
        case let val as Double:
            try container.encode(val)
        case let val as Bool:
            try container.encode(val)
        case let val as [String: Any]:
            try container.encode(DictionaryCodable(val))
        case let val as [[String: Any]]:
            try container.encode(val.map { DictionaryCodable($0) })
        default:
            throw EncodingError.invalidValue(value, EncodingError.Context(codingPath: encoder.codingPath, debugDescription: "Unsupported type"))
        }
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        if let str = try? container.decode(String.self) {
            value = str
        } else if let int = try? container.decode(Int.self) {
            value = int
        } else if let dbl = try? container.decode(Double.self) {
            value = dbl
        } else if let bool = try? container.decode(Bool.self) {
            value = bool
        } else if let dict = try? container.decode(DictionaryCodable.self) {
            value = dict.value
        } else if let array = try? container.decode([DictionaryCodable].self) {
            value = array.map { $0.value }
        } else {
            throw DecodingError.dataCorruptedError(in: container, debugDescription: "Unsupported JSON type")
        }
    }
}

/// Helper struct to encode/decode `[String: Any]`
struct DictionaryCodable: Codable {
    let value: [String: Any]

    init(_ value: [String: Any]) {
        self.value = value
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: JSONCodingKeys.self)
        for (key, value) in value {
            let codingKey = JSONCodingKeys(stringValue: key)
            switch value {
            case let str as String:
                try container.encode(str, forKey: codingKey)
            case let int as Int:
                try container.encode(int, forKey: codingKey)
            case let dbl as Double:
                try container.encode(dbl, forKey: codingKey)
            case let bool as Bool:
                try container.encode(bool, forKey: codingKey)
            case let dict as [String: Any]:
                try container.encode(DictionaryCodable(dict), forKey: codingKey)
            case let array as [[String: Any]]:
                try container.encode(array.map { DictionaryCodable($0) }, forKey: codingKey)
            default:
                throw EncodingError.invalidValue(value, EncodingError.Context(codingPath: encoder.codingPath, debugDescription: "Unsupported dictionary value"))
            }
        }
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: JSONCodingKeys.self)
        var dict = [String: Any]()
        for key in container.allKeys {
            if let str = try? container.decode(String.self, forKey: key) {
                dict[key.stringValue] = str
            } else if let int = try? container.decode(Int.self, forKey: key) {
                dict[key.stringValue] = int
            } else if let dbl = try? container.decode(Double.self, forKey: key) {
                dict[key.stringValue] = dbl
            } else if let bool = try? container.decode(Bool.self, forKey: key) {
                dict[key.stringValue] = bool
            } else if let nestedDict = try? container.decode(DictionaryCodable.self, forKey: key) {
                dict[key.stringValue] = nestedDict.value
            } else if let nestedArray = try? container.decode([DictionaryCodable].self, forKey: key) {
                dict[key.stringValue] = nestedArray.map { $0.value }
            }
        }
        self.value = dict
    }
}

/// Custom CodingKeys to handle dynamic keys
struct JSONCodingKeys: CodingKey {
    var stringValue: String
    var intValue: Int? { nil }

    init(stringValue: String) {
        self.stringValue = stringValue
    }

    init?(intValue: Int) {
        return nil
    }
}

extension ClientResponse {
    func decode<T: Content & Decodable>(_ type: T.Type, on eventLoop: EventLoop) -> EventLoopFuture<T> {
        guard let body = self.body else {
            return eventLoop.makeFailedFuture(Abort(.internalServerError, reason: "Empty response body"))
        }
        
        do {
            let decodedData = try JSONDecoder().decode(T.self, from: Data(buffer: body))
            return eventLoop.makeSucceededFuture(decodedData)
        } catch {
            return eventLoop.makeFailedFuture(error)
        }
    }
}


extension GraphQLController {
    
    //Step 1 Fetch the network
    static func fetchConnectedNetwork(req: Request) throws -> EventLoopFuture<ConnectedNetworkResponse> {
        return try GraphQLController.kioskLogin(req: req).flatMap { response in
            let token  = response.login.user?.token ?? ""
            let service = GraphQLService(client: req.client, token: token)
            return try! service.create(service: req, input: FetchConnectedNetworkQuery(id: schedulerParentOrg), token: token).flatMap { response in
                response.decode(ConnectedNetworkResponse.self, on: req.eventLoop)
            }
        }
    }
    
    //Step 2 Fetch the next avaiable appointments for the servies
    static func fetchNextAviableApts(req: Request, id: String) throws -> EventLoopFuture<NextApptNetworkResponse> {
        return try GraphQLController.kioskLogin(req: req).flatMap { response in
            let token  = response.login.user?.token ?? ""
            let service = GraphQLService(client: req.client, token: token)
            return try! service.create(service: req, input: NextAppointmentQuery(id: id), token: token).flatMap { response in
                response.decode(NextApptNetworkResponse.self, on: req.eventLoop)
            }
        }
    }
    
    //Step 3 Get avaiable times for service provider.
    static func fetchAppointmentProviderTimes(req: Request, input: ConnnectedApptLookupInput) throws -> EventLoopFuture<AppointmentTimesResponse> {
        return try GraphQLController.kioskLogin(req: req).flatMap { response in
            let token  = response.login.user?.token ?? ""
            let service = GraphQLService(client: req.client, token: token)
            return try! service.create(service: req, input: ConnectedAppointmentProviderTimesQuery(org: input.org,
                                                                                                   service: input.service,
                                                                                                   date: input.date,
                                                                                                   provider:
                                                                                                    input.provider), token: token).flatMap { response in
                response.decode(AppointmentTimesResponse.self, on: req.eventLoop)
            }
        }
    }
    
    //Helpers Get service providers and services.
    static func servicesAndProvidersQuery(req: Request, id: String) throws -> EventLoopFuture<ProvidersAndServicesResponse> {
        return try GraphQLController.kioskLogin(req: req).flatMap { response in
            let token  = response.login.user?.token ?? ""
            let service = GraphQLService(client: req.client, token: token)
            return try! service.create(service: req, input: ServicesAndProvidersQuery(id: id), token: token).flatMap { response in
                response.decode(ProvidersAndServicesResponse.self, on: req.eventLoop)
            }
        }
    }
}
