//
//  ProgramsController.swift
//  
//
//  Created by Augment Agent on 6/27/25.
//

import Foundation
import Fluent
import Vapor

struct ProgramsController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        // Member-specific program routes (using /api prefix to match other medical controllers)
        let memberPrograms = routes.grouped("api", "members", ":memberID", "programs")
        memberPrograms.get(use: getMemberPrograms)
        memberPrograms.post(use: createMemberProgram)
        memberPrograms.group(":programID") { program in
            program.get(use: getMemberProgram)
            program.put(use: updateMemberProgram)
            program.delete(use: deleteMemberProgram)
        }

        // Program task routes (using /api prefix for consistency)
        let programTasks = routes.grouped("api", "programs", ":programID", "tasks")
        programTasks.get(use: getProgramTasks)
        programTasks.post(use: createProgramTask)
        programTasks.group(":taskID") { task in
            task.get(use: getProgramTask)
            task.put(use: updateProgramTask)
            task.delete(use: deleteProgramTask)
        }

        // Review period routes (using /api prefix for consistency)
        let reviewPeriods = routes.grouped("api", "programs", ":programID", "periods")
        reviewPeriods.get(use: getReviewPeriods)
        reviewPeriods.post(use: createReviewPeriod)
        reviewPeriods.group(":periodID") { period in
            period.get(use: getReviewPeriod)
            period.put(use: updateReviewPeriod)
            period.delete(use: deleteReviewPeriod)
        }

        // Timeline routes for programs (using /api prefix for consistency)
        let programTimeline = routes.grouped("api", "programs", ":programID", "timeline")
        programTimeline.get(use: getProgramTimeline)
    }
    
    // MARK: - Member Program Operations
    
    func getMemberPrograms(req: Request) async throws -> [ProgramResponse] {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        let programs = try await Program.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .with(\.$reviewPeriods) { reviewPeriod in
                reviewPeriod.with(\.$tasks)
            }
            .all()
        
        return programs.map { program in
            let reviewPeriodResponses = program.reviewPeriods.map { period in
                let taskResponses = period.tasks.map(ProgramTaskResponse.init)
                return ReviewPeriodResponse(reviewPeriod: period, tasks: taskResponses)
            }
            return ProgramResponse(program: program, reviewPeriods: reviewPeriodResponses)
        }
    }
    
    func createMemberProgram(req: Request) async throws -> ProgramResponse {
        guard let memberID = req.parameters.get("memberID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        // Verify member exists
        guard let member = try await Member.find(memberID, on: req.db) else {
            throw Abort(.notFound, reason: "Member not found")
        }
        
        let createRequest = try req.content.decode(ProgramCreateRequest.self)
        
        let program = Program(
            programType: createRequest.programType,
            displayName: createRequest.displayName,
            startDate: createRequest.startDate,
            endDate: createRequest.endDate,
            status: createRequest.status,
            reviewFrequencyDays: createRequest.reviewFrequencyDays,
            assignedBy: createRequest.assignedBy,
            assignedTo: createRequest.assignedTo,
            assignedToId: createRequest.assignedToId
        )
        
        program.$member.id = memberID
        try await program.save(on: req.db)

        // Create timeline entry for Program creation
        let user = try await TokenController.fullUserFromToken(req: req)
        try await ProgramTimelineService.createProgramTimeline(
            operation: .created,
            program: program,
            on: req.db,
            creatorID: user.requireID(),
            memberID: memberID
        )

        // Create review periods and tasks
        var reviewPeriodResponses: [ReviewPeriodResponse] = []
        
        for periodRequest in createRequest.reviewPeriods {
            let reviewPeriod = ReviewPeriod(
                periodNumber: periodRequest.periodNumber,
                startDate: periodRequest.startDate,
                endDate: periodRequest.endDate,
                status: periodRequest.status,
                title: periodRequest.title,
                outcomeStatus: periodRequest.outcomeStatus,
                outcomeDescription: periodRequest.outcomeDescription,
                assignedToId: periodRequest.assignedToId
            )
            
            reviewPeriod.$program.id = try program.requireID()
            try await reviewPeriod.save(on: req.db)

            // Create timeline entry for ReviewPeriod creation
            try await ProgramTimelineService.createReviewPeriodTimeline(
                operation: .created,
                reviewPeriod: reviewPeriod,
                on: req.db,
                creatorID: user.requireID(),
                memberID: memberID
            )

            var taskResponses: [ProgramTaskResponse] = []
            
            for taskRequest in periodRequest.tasks {
                let task = ProgramTask(
                    taskType: taskRequest.taskType,
                    title: taskRequest.title,
                    assessmentKey: taskRequest.assessmentKey,
                    reviewKey: taskRequest.reviewKey,
                    assignedTo: taskRequest.assignedTo,
                    completedAt: taskRequest.completedAt,
                    dueDate: taskRequest.dueDate,
                    status: taskRequest.status,
                    assignedToId: taskRequest.assignedToId
                )
                
                task.$reviewPeriod.id = try reviewPeriod.requireID()
                try await task.save(on: req.db)

                // Schedule SMS reminder for program task
                try await scheduleProgramTaskReminders(req: req, task: task, programID: try program.requireID(), name: user.fullName(), phone: user.smsPhone())

                // Create timeline entry for ProgramTask creation
                try await ProgramTimelineService.createProgramTaskTimeline(
                    operation: .created,
                    task: task,
                    on: req.db,
                    creatorID: user.requireID(),
                    memberID: memberID
                )

                taskResponses.append(ProgramTaskResponse(task: task))
            }
            
            reviewPeriodResponses.append(ReviewPeriodResponse(reviewPeriod: reviewPeriod, tasks: taskResponses))
        }
        
        return ProgramResponse(program: program, reviewPeriods: reviewPeriodResponses)
    }
    
    func getMemberProgram(req: Request) async throws -> ProgramResponse {
        guard let memberID = req.parameters.get("memberID", as: UUID.self),
              let programID = req.parameters.get("programID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member or program ID")
        }
        
        let program = try await Program.query(on: req.db)
            .filter(\.$id == programID)
            .filter(\.$member.$id == memberID)
            .with(\.$reviewPeriods) { reviewPeriod in
                reviewPeriod.with(\.$tasks)
            }
            .first()

        guard let program = program else {
            throw Abort(.notFound, reason: "Program not found")
        }
        
        let reviewPeriodResponses = program.reviewPeriods.map { period in
            let taskResponses = period.tasks.map(ProgramTaskResponse.init)
            return ReviewPeriodResponse(reviewPeriod: period, tasks: taskResponses)
        }
        
        return ProgramResponse(program: program, reviewPeriods: reviewPeriodResponses)
    }
    
    func updateMemberProgram(req: Request) async throws -> ProgramResponse {
        guard let memberID = req.parameters.get("memberID", as: UUID.self),
              let programID = req.parameters.get("programID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member or program ID")
        }
        
        guard let program = try await Program.query(on: req.db)
            .filter(\.$id == programID)
            .filter(\.$member.$id == memberID)
            .first() else {
            throw Abort(.notFound, reason: "Program not found")
        }
        
        let updateRequest = try req.content.decode(ProgramUpdateRequest.self)
        
        if let programType = updateRequest.programType {
            program.programType = programType
        }
        if let displayName = updateRequest.displayName {
            program.displayName = displayName
        }
        if let startDate = updateRequest.startDate {
            program.startDate = startDate
        }
        if let endDate = updateRequest.endDate {
            program.endDate = endDate
        }
        if let status = updateRequest.status {
            program.status = status
        }
        if let reviewFrequencyDays = updateRequest.reviewFrequencyDays {
            program.reviewFrequencyDays = reviewFrequencyDays
        }
        if let assignedBy = updateRequest.assignedBy {
            program.assignedBy = assignedBy
        }
        if let assignedTo = updateRequest.assignedTo {
            program.assignedTo = assignedTo
        }
        if let assignedToId = updateRequest.assignedToId {
            program.$assignedToUser.id = assignedToId
        }

        try await program.save(on: req.db)

        // Create timeline entry for Program update
        let user = try await TokenController.userIdFromToken(req: req)
        try await ProgramTimelineService.createProgramTimeline(
            operation: .updated,
            program: program,
            on: req.db,
            creatorID: user,
            memberID: memberID
        )

        return ProgramResponse(program: program)
    }
    
    func deleteMemberProgram(req: Request) async throws -> HTTPStatus {
        guard let memberID = req.parameters.get("memberID", as: UUID.self),
              let programID = req.parameters.get("programID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid member or program ID")
        }
        
        guard let program = try await Program.query(on: req.db)
            .filter(\.$id == programID)
            .filter(\.$member.$id == memberID)
            .first() else {
            throw Abort(.notFound, reason: "Program not found")
        }

        // You cant create timeline item for a program thats going to get deleted
//        let user = try await TokenController.userIdFromToken(req: req)
//        try await ProgramTimelineService.createProgramTimeline(
//            operation: .deleted,
//            program: program,
//            on: req.db,
//            creatorID: user,
//            memberID: memberID
//        )

        try await program.delete(on: req.db)
        return .noContent
    }
    
    // MARK: - Program Task Operations
    
    func getProgramTasks(req: Request) async throws -> [ProgramTaskResponse] {
        guard let programID = req.parameters.get("programID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid program ID")
        }
        
        let tasks = try await ProgramTask.query(on: req.db)
            .join(ReviewPeriod.self, on: \ProgramTask.$reviewPeriod.$id == \ReviewPeriod.$id)
            .filter(ReviewPeriod.self, \.$program.$id == programID)
            .all()
        
        return tasks.map(ProgramTaskResponse.init)
    }
    
    func createProgramTask(req: Request) async throws -> ProgramTaskResponse {
        guard let programID = req.parameters.get("programID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid program ID")
        }
        
        let createRequest = try req.content.decode(ProgramTaskCreateRequest.self)
        
        // Find an active review period for this program
        guard let reviewPeriod = try await ReviewPeriod.query(on: req.db)
            .filter(\.$program.$id == programID)
            .filter(\.$status == "active")
            .first() else {
            throw Abort(.badRequest, reason: "No active review period found for this program")
        }
        
        let task = ProgramTask(
            taskType: createRequest.taskType,
            title: createRequest.title,
            assessmentKey: createRequest.assessmentKey,
            reviewKey: createRequest.reviewKey,
            assignedTo: createRequest.assignedTo,
            completedAt: createRequest.completedAt,
            dueDate: createRequest.dueDate,
            status: createRequest.status,
            assignedToId: createRequest.assignedToId
        )
        
        task.$reviewPeriod.id = try reviewPeriod.requireID()
        try await task.save(on: req.db)

        // Create timeline entry for ProgramTask creation
        let user = try await TokenController.fullUserFromToken(req: req)
        
        // Schedule SMS reminder for program task
        try await scheduleProgramTaskReminders(req: req, task: task, programID: programID, name: user.fullName(), phone: user.smsPhone())
        
        
        try await ProgramTimelineService.createProgramTaskTimeline(
            operation: .created,
            task: task,
            on: req.db,
            creatorID: user.requireID()
        )

        return ProgramTaskResponse(task: task)
    }
    
    func getProgramTask(req: Request) async throws -> ProgramTaskResponse {
        guard let taskID = req.parameters.get("taskID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid task ID")
        }
        
        guard let task = try await ProgramTask.find(taskID, on: req.db) else {
            throw Abort(.notFound, reason: "Task not found")
        }
        
        return ProgramTaskResponse(task: task)
    }
    
    func updateProgramTask(req: Request) async throws -> ProgramTaskResponse {
        guard let taskID = req.parameters.get("taskID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid task ID")
        }
        
        guard let task = try await ProgramTask.find(taskID, on: req.db) else {
            throw Abort(.notFound, reason: "Task not found")
        }
        
        let updateRequest = try req.content.decode(ProgramTaskUpdateRequest.self)
        
        if let assignedTo = updateRequest.assignedTo {
            task.assignedTo = assignedTo
        }
        if let assignedToId = updateRequest.assignedToId {
            task.$assignedToUser.id = assignedToId
        }
        if let completedAt = updateRequest.completedAt {
            task.completedAt = completedAt
        }
        if let status = updateRequest.status {
            task.status = status
        }
        
        try await task.save(on: req.db)

        // Handle program task reminder scheduling/cancellation
        try await handleProgramTaskReminderUpdate(req: req, task: task, updateRequest: updateRequest)

        // Create timeline entry for ProgramTask update
        let user = try await TokenController.userIdFromToken(req: req)
        try await ProgramTimelineService.createProgramTaskTimeline(
            operation: .updated,
            task: task,
            on: req.db,
            creatorID: user
        )

        return ProgramTaskResponse(task: task)
    }
    
    func deleteProgramTask(req: Request) async throws -> HTTPStatus {
        guard let taskID = req.parameters.get("taskID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid task ID")
        }
        
        guard let task = try await ProgramTask.find(taskID, on: req.db) else {
            throw Abort(.notFound, reason: "Task not found")
        }

        // Create timeline entry for ProgramTask deletion before deleting
        let user = try await TokenController.userIdFromToken(req: req)
        try await ProgramTimelineService.createProgramTaskTimeline(
            operation: .deleted,
            task: task,
            on: req.db,
            creatorID: user
        )

        try await task.delete(on: req.db)
        return .noContent
    }

    // MARK: - Review Period Operations

    func getReviewPeriods(req: Request) async throws -> [ReviewPeriodResponse] {
        guard let programID = req.parameters.get("programID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid program ID")
        }

        let reviewPeriods = try await ReviewPeriod.query(on: req.db)
            .filter(\.$program.$id == programID)
            .with(\.$tasks)
            .all()

        return reviewPeriods.map { period in
            let taskResponses = period.tasks.map(ProgramTaskResponse.init)
            return ReviewPeriodResponse(reviewPeriod: period, tasks: taskResponses)
        }
    }

    func createReviewPeriod(req: Request) async throws -> ReviewPeriodResponse {
        guard let programID = req.parameters.get("programID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid program ID")
        }

        // Verify program exists
        guard let _ = try await Program.find(programID, on: req.db) else {
            throw Abort(.notFound, reason: "Program not found")
        }

        let createRequest = try req.content.decode(ReviewPeriodCreateRequest.self)

        let reviewPeriod = ReviewPeriod(
            periodNumber: createRequest.periodNumber,
            startDate: createRequest.startDate,
            endDate: createRequest.endDate,
            status: createRequest.status,
            title: createRequest.title,
            outcomeStatus: createRequest.outcomeStatus,
            outcomeDescription: createRequest.outcomeDescription,
            assignedToId: createRequest.assignedToId
        )

        reviewPeriod.$program.id = programID
        try await reviewPeriod.save(on: req.db)

        // Create timeline entry for ReviewPeriod creation
        let user = try await TokenController.fullUserFromToken(req: req)
        
        try await ProgramTimelineService.createReviewPeriodTimeline(
            operation: .created,
            reviewPeriod: reviewPeriod,
            on: req.db,
            creatorID: user.requireID()
        )

        var taskResponses: [ProgramTaskResponse] = []

        for taskRequest in createRequest.tasks {
            let task = ProgramTask(
                taskType: taskRequest.taskType,
                title: taskRequest.title,
                assessmentKey: taskRequest.assessmentKey,
                reviewKey: taskRequest.reviewKey,
                assignedTo: taskRequest.assignedTo,
                completedAt: taskRequest.completedAt,
                dueDate: taskRequest.dueDate,
                status: taskRequest.status,
                assignedToId: taskRequest.assignedToId
            )

            task.$reviewPeriod.id = try reviewPeriod.requireID()
            try await task.save(on: req.db)

            // Schedule SMS reminder for program task
            try await scheduleProgramTaskReminders(req: req, task: task, programID: programID, name: user.fullName(), phone: user.smsPhone())

            // Create timeline entry for ProgramTask creation
            try await ProgramTimelineService.createProgramTaskTimeline(
                operation: .created,
                task: task,
                on: req.db,
                creatorID: user.requireID()
            )

            taskResponses.append(ProgramTaskResponse(task: task))
        }

        return ReviewPeriodResponse(reviewPeriod: reviewPeriod, tasks: taskResponses)
    }

    func getReviewPeriod(req: Request) async throws -> ReviewPeriodResponse {
        guard let periodID = req.parameters.get("periodID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid period ID")
        }

        guard let reviewPeriod = try await ReviewPeriod.query(on: req.db)
            .filter(\.$id == periodID)
            .with(\.$tasks)
            .first() else {
            throw Abort(.notFound, reason: "Review period not found")
        }

        let taskResponses = reviewPeriod.tasks.map(ProgramTaskResponse.init)
        return ReviewPeriodResponse(reviewPeriod: reviewPeriod, tasks: taskResponses)
    }

    func updateReviewPeriod(req: Request) async throws -> ReviewPeriodResponse {
        guard let periodID = req.parameters.get("periodID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid period ID")
        }

        guard let reviewPeriod = try await ReviewPeriod.find(periodID, on: req.db) else {
            throw Abort(.notFound, reason: "Review period not found")
        }

        let updateRequest = try req.content.decode(ReviewPeriodUpdateRequest.self)

        if let periodNumber = updateRequest.periodNumber {
            reviewPeriod.periodNumber = periodNumber
        }
        if let startDate = updateRequest.startDate {
            reviewPeriod.startDate = startDate
        }
        if let endDate = updateRequest.endDate {
            reviewPeriod.endDate = endDate
        }
        if let status = updateRequest.status {
            reviewPeriod.status = status
        }
        if let title = updateRequest.title {
            reviewPeriod.title = title
        }
        if let outcomeStatus = updateRequest.outcomeStatus {
            reviewPeriod.outcomeStatus = outcomeStatus
        }
        if let outcomeDescription = updateRequest.outcomeDescription {
            reviewPeriod.outcomeDescription = outcomeDescription
        }
        if let assignedToId = updateRequest.assignedToId {
            reviewPeriod.$assignedToUser.id = assignedToId
        }

        try await reviewPeriod.save(on: req.db)

        // Create timeline entry for ReviewPeriod update
        let user = try await TokenController.userIdFromToken(req: req)
        try await ProgramTimelineService.createReviewPeriodTimeline(
            operation: .updated,
            reviewPeriod: reviewPeriod,
            on: req.db,
            creatorID: user
        )

        return ReviewPeriodResponse(reviewPeriod: reviewPeriod)
    }

    func deleteReviewPeriod(req: Request) async throws -> HTTPStatus {
        guard let periodID = req.parameters.get("periodID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid period ID")
        }

        guard let reviewPeriod = try await ReviewPeriod.find(periodID, on: req.db) else {
            throw Abort(.notFound, reason: "Review period not found")
        }

        // Create timeline entry for ReviewPeriod deletion before deleting
        let user = try await TokenController.userIdFromToken(req: req)
        try await ProgramTimelineService.createReviewPeriodTimeline(
            operation: .deleted,
            reviewPeriod: reviewPeriod,
            on: req.db,
            creatorID: user
        )

        try await reviewPeriod.delete(on: req.db)
        return .noContent
    }

    // MARK: - Program Timeline Operations

    func getProgramTimeline(req: Request) async throws -> [TimelineItem] {
        guard let programID = req.parameters.get("programID", as: UUID.self) else {
            throw Abort(.badRequest, reason: "Invalid program ID")
        }

        // Verify program exists
        guard try await Program.find(programID, on: req.db) != nil else {
            throw Abort(.notFound, reason: "Program not found")
        }

        // Fetch timeline items for this program
        let timelineItems = try await TimelineItem.query(on: req.db)
            .filter(\.$program.$id == programID)
            .filter(\.$visible == true)
            .with(\.$creator) { user in
                user.with(\.$attachments)
            }
            .sort(\.$createdAt, .descending)
            .all()

        return timelineItems
    }

    // MARK: - SMS Reminder Scheduling for Program Tasks

    private func scheduleProgramTaskReminders(req: Request, task: ProgramTask, programID: UUID, name: String, phone: String?) async throws {
        // Only schedule reminders if we have a valid due date and task ID
        guard let taskId = task.id else {
            req.logger.info("Skipping SMS reminder scheduling for program task - missing task ID")
            return
        }

        guard let phoneNumber = phone, !phoneNumber.isEmpty else {
            req.logger.info("Skipping SMS reminder scheduling for program task - missing phone number")
            return
        }
        
        let dueDate = task.dueDate
        let memberName = name
        let taskTitle = task.title

        do {
            _ = try await req.smsReminderScheduler.scheduleProgramTaskReminder(
                req: req,
                programTaskId: taskId,
                phoneNumber: phoneNumber,
                dueDate: dueDate,
                memberName: memberName,
                taskTitle: taskTitle
            ).get()
        } catch {
            req.logger.error("Failed to schedule SMS reminders for program task \(taskId): \(error)")
            // Don't fail the task creation if reminder scheduling fails
        }
    }

    private func handleProgramTaskReminderUpdate(req: Request, task: ProgramTask, updateRequest: ProgramTaskUpdateRequest) async throws {
        let isCompleted = updateRequest.status?.lowercased() == "completed" || updateRequest.status?.lowercased() == "complete"

        if isCompleted {
            // Cancel reminders if task is completed
            try await cancelProgramTaskReminders(req: req, taskId: task.id!)
        } else {
            // Note: For program tasks, we don't reschedule on update since due dates typically don't change
            // If due date updates are needed in the future, add that logic here
        }
    }

    private func cancelProgramTaskReminders(req: Request, taskId: UUID) async throws {
        do {
            _ = try await req.smsReminderScheduler.cancelReminders(req: req, entityId: taskId, entityType: .programTask).get()
        } catch {
            req.logger.error("Failed to cancel SMS reminders for program task \(taskId): \(error)")
            // Don't fail the operation if reminder cancellation fails
        }
    }
}
