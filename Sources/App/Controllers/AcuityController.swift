//
//  AcuityController.swift
//  hmbl-core
//
//  Created by Augment Agent on 9/25/25.
//

import Foundation
import Vapor
import Fluent

// MARK: - Request/Response Models
struct CreateAcuityScoreRequest: Content {
    let score: Int
    let level: String
    let source: String
    let notes: String?
    let references: [AcuityReference]
}

struct AcuityScoreResponse: Content {
    let id: UUID?
    let score: Int
    let level: String
    let source: String
    let notes: String?
    let references: [AcuityReference]
    let createdByUser: UserInfo?
    let createdAt: Date?
    let updatedAt: Date?

    init(from acuityScore: AcuityScore) {
        self.id = acuityScore.id
        self.score = acuityScore.score
        self.level = acuityScore.level
        self.source = acuityScore.source
        self.notes = acuityScore.notes
        self.references = acuityScore.references
        self.createdByUser = acuityScore.createdByUser.map { UserInfo(from: $0) }
        self.createdAt = acuityScore.createdAt
        self.updatedAt = acuityScore.updatedAt
    }
}

struct UserInfo: Content {
    let id: UUID?
    let firstName: String
    let lastName: String
    let email: String

    init(from user: User) {
        self.id = user.id
        self.firstName = user.firstName
        self.lastName = user.lastName
        self.email = user.email
    }
}

struct AcuityHistoryResponse: Content {
    let score: Int
    let level: String
    let createdAt: Date?
    let source: String
}

// MARK: - Controller
struct AcuityController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let acuity = routes.grouped("api", "members", ":memberID", "acuity")
        acuity.get(use: getLatestAcuityScore)
        acuity.post(use: createAcuityScore)
        acuity.get("history", use: getAcuityHistory)
    }
    
    // MARK: - GET /api/members/:id/acuity
    func getLatestAcuityScore(req: Request) throws -> EventLoopFuture<AcuityScoreResponse> {
        guard let memberID = req.parameters.get("memberID"),
              let memberUUID = UUID(memberID) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        return AcuityScore.query(on: req.db)
            .filter(\.$member.$id == memberUUID)
            .with(\.$createdByUser)
            .sort(\.$createdAt, .descending)
            .first()
            .flatMapThrowing { acuityScore in
                guard let score = acuityScore else {
                    throw Abort(.notFound, reason: "No acuity score found for member")
                }
                return AcuityScoreResponse(from: score)
            }
    }
    
    // MARK: - POST /api/members/:id/acuity
    func createAcuityScore(req: Request) throws -> EventLoopFuture<AcuityScoreResponse> {
        guard let memberID = req.parameters.get("memberID"),
              let memberUUID = UUID(memberID) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        let createRequest = try req.content.decode(CreateAcuityScoreRequest.self)
        
        // Validate level
        let validLevels = ["Low", "Moderate", "High"]
        guard validLevels.contains(createRequest.level) else {
            throw Abort(.badRequest, reason: "Invalid level. Must be Low, Moderate, or High")
        }
        
        // Verify member exists
        return Member.find(memberUUID, on: req.db)
            .flatMapThrowing { member in
                guard member != nil else {
                    throw Abort(.notFound, reason: "Member not found")
                }
                return member!
            }
            .flatMap { (member: Member) in
                // Get current user for created_by_user_id
                return (try? AuthController.userFromToken(req: req)) ?? req.eventLoop.future(error: Abort(.unauthorized))
            }
            .flatMap { currentUser in

                let acuityScore = AcuityScore(
                    score: createRequest.score,
                    level: createRequest.level,
                    source: createRequest.source,
                    notes: createRequest.notes,
                    references: createRequest.references,
                    createdByUserId: currentUser.id
                )
                
                acuityScore.$member.id = memberUUID
                
                return acuityScore.save(on: req.db)
                    .flatMap { _ in
                        // Log to timeline
                        return self.logAcuityToTimeline(
                            acuityScore: acuityScore,
                            memberID: memberUUID,
                            req: req
                        )
                    }
                    .flatMap { _ in
                        // Return the created score with relationships
                        return AcuityScore.query(on: req.db)
                            .filter(\.$id == acuityScore.id!)
                            .with(\.$createdByUser)
                            .first()
                            .flatMapThrowing { savedScore in
                                guard let score = savedScore else {
                                    throw Abort(.internalServerError, reason: "Failed to retrieve created acuity score")
                                }
                                return AcuityScoreResponse(from: score)
                            }
                    }
            }
    }
    
    // MARK: - GET /api/members/:id/acuity/history
    func getAcuityHistory(req: Request) throws -> EventLoopFuture<[AcuityHistoryResponse]> {
        guard let memberID = req.parameters.get("memberID"),
              let memberUUID = UUID(memberID) else {
            throw Abort(.badRequest, reason: "Invalid member ID")
        }
        
        return AcuityScore.query(on: req.db)
            .filter(\.$member.$id == memberUUID)
            .sort(\.$createdAt, .descending)
            .all()
            .map { scores in
                scores.map { score in
                    AcuityHistoryResponse(
                        score: score.score,
                        level: score.level,
                        createdAt: score.createdAt,
                        source: score.source
                    )
                }
            }
    }
    
    // MARK: - Timeline Logging
    private func logAcuityToTimeline(
        acuityScore: AcuityScore,
        memberID: UUID,
        req: Request
    ) -> EventLoopFuture<Void> {
        let referencesDescription = acuityScore.references.compactMap { ref in
            if let title = ref.title {
                return "\(ref.type): \(title)"
            }
            return ref.type
        }.joined(separator: ", ")
        
        let description = "Score set to \(acuityScore.level) (\(acuityScore.score)). \(referencesDescription)"
        
        let refIds = acuityScore.references.map { "\($0.type):\($0.refId)" }
        
        let meta = MetaData(
            data: [
                "acuity_score": "\(acuityScore.score)",
                "acuity_level": acuityScore.level,
                "source": acuityScore.source,
                "ref_ids": refIds.joined(separator: ",")
            ]
        )
        
        let timelineItem = TimelineItem(
            carepackageID: "member-\(memberID)",
            status: "success",
            desc: description,
            title: "Acuity Updated",
            memberId: memberID,
            visible: true,
            meta: meta
        )
        
        return timelineItem.save(on: req.db).transform(to: ())
    }
}
