//
//  Program.swift
//  
//
//  Created by Augment Agent on 6/27/25.
//

import Foundation
import Fluent
import Vapor

// MARK: - Program Model
final class Program: Model, @unchecked Sendable {
    static let schema = "programs"
    
    @ID var id: UUID?
    @Parent(key: "member_id") var member: Member

    @Field(key: "program_type") var programType: String
    @Field(key: "display_name") var displayName: String
    @Field(key: "start_date") var startDate: Date
    @Field(key: "end_date") var endDate: Date
    @Field(key: "status") var status: String
    @Field(key: "review_frequency_days") var reviewFrequencyDays: Int
    @Field(key: "assigned_by") var assignedBy: String
    @Field(key: "assigned_to") var assignedTo: String

    // New UUID-based assignment fields
    @OptionalParent(key: "assigned_to_id") var assignedToUser: User?

    @Children(for: \.$program)
    var reviewPeriods: [ReviewPeriod]

    @Children(for: \.$program)
    var timelineItems: [TimelineItem]

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         programType: String,
         displayName: String,
         startDate: Date,
         endDate: Date,
         status: String,
         reviewFrequencyDays: Int,
         assignedBy: String,
         assignedTo: String,
         assignedToId: UUID? = nil) {
        self.id = id
        self.programType = programType
        self.displayName = displayName
        self.startDate = startDate
        self.endDate = endDate
        self.status = status
        self.reviewFrequencyDays = reviewFrequencyDays
        self.assignedBy = assignedBy
        self.assignedTo = assignedTo
        if let assignedToId = assignedToId {
            self.$assignedToUser.id = assignedToId
        }
    }
}

// MARK: - Program Content Conformance
extension Program: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case programType = "program_type"
        case displayName = "display_name"
        case startDate = "start_date"
        case endDate = "end_date"
        case status
        case reviewFrequencyDays = "review_frequency_days"
        case assignedBy = "assigned_by"
        case assignedTo = "assigned_to"
        case reviewPeriods = "review_periods"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: member is excluded from coding keys to prevent decoding issues
    }
}

// MARK: - ReviewPeriod Model
final class ReviewPeriod: Model, @unchecked Sendable {
    static let schema = "review_periods"
    
    @ID var id: UUID?
    @Parent(key: "program_id") var program: Program
    
    @Field(key: "period_number") var periodNumber: Int
    @Field(key: "start_date") var startDate: Date
    @Field(key: "end_date") var endDate: Date
    @Field(key: "status") var status: String
    @OptionalField(key: "title") var title: String?
    @Field(key: "outcome_status") var outcomeStatus: String?
    @Field(key: "outcome_description") var outcomeDescription: String?

    // New UUID-based assignment fields
    @OptionalParent(key: "assigned_to_id") var assignedToUser: User?

    @Children(for: \.$reviewPeriod)
    var tasks: [ProgramTask]
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         periodNumber: Int,
         startDate: Date,
         endDate: Date,
         status: String,
         title: String? = nil,
         outcomeStatus: String? = nil,
         outcomeDescription: String? = nil,
         assignedToId: UUID? = nil) {
        self.id = id
        self.periodNumber = periodNumber
        self.startDate = startDate
        self.endDate = endDate
        self.status = status
        self.title = title
        self.outcomeStatus = outcomeStatus
        self.outcomeDescription = outcomeDescription
        if let assignedToId = assignedToId {
            self.$assignedToUser.id = assignedToId
        }
    }
}

// MARK: - ReviewPeriod Content Conformance
extension ReviewPeriod: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case periodNumber = "period_number"
        case startDate = "start_date"
        case endDate = "end_date"
        case status
        case outcomeStatus = "outcome_status"
        case outcomeDescription = "outcome_description"
        case tasks
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: program is excluded from coding keys to prevent decoding issues
    }
}

// MARK: - ProgramTask Model
final class ProgramTask: Model, @unchecked Sendable {
    static let schema = "program_tasks"
    
    @ID var id: UUID?
    @Parent(key: "review_period_id") var reviewPeriod: ReviewPeriod
    
    @Field(key: "task_type") var taskType: String
    @Field(key: "title") var title: String
    @OptionalField(key: "assessment_key") var assessmentKey: String?
    @OptionalField(key: "review_key") var reviewKey: String?
    @OptionalField(key: "assigned_to") var assignedTo: String?
    @OptionalField(key: "completed_at") var completedAt: Date?
    @Field(key: "due_date") var dueDate: Date
    @Field(key: "status") var status: String

    // New UUID-based assignment field
    @OptionalParent(key: "assigned_to_id") var assignedToUser: User?
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         taskType: String,
         title: String,
         assessmentKey: String? = nil,
         reviewKey: String? = nil,
         assignedTo: String? = nil,
         completedAt: Date? = nil,
         dueDate: Date,
         status: String,
         assignedToId: UUID? = nil) {
        self.id = id
        self.taskType = taskType
        self.title = title
        self.assessmentKey = assessmentKey
        self.reviewKey = reviewKey
        self.assignedTo = assignedTo
        self.completedAt = completedAt
        self.dueDate = dueDate
        self.status = status
        if let assignedToId = assignedToId {
            self.$assignedToUser.id = assignedToId
        }
    }
}

// MARK: - ProgramTask Content Conformance
extension ProgramTask: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case taskType = "task_type"
        case title
        case assessmentKey = "assessment_key"
        case reviewKey = "review_key"
        case assignedTo = "assigned_to"
        case completedAt = "completed_at"
        case dueDate = "due_date"
        case status
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: reviewPeriod is excluded from coding keys to prevent decoding issues
    }
}

// MARK: - Migrations

struct CreateProgram: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("programs")
            .id()
            .field("member_id", .uuid, .required, .references("members", "id", onDelete: .cascade))
            .field("program_type", .string, .required)
            .field("display_name", .string, .required)
            .field("start_date", .date, .required)
            .field("end_date", .date, .required)
            .field("status", .string, .required)
            .field("review_frequency_days", .int, .required)
            .field("assigned_by", .string, .required)
            .field("assigned_to", .string, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("programs").delete()
    }
}

struct CreateReviewPeriod: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("review_periods")
            .id()
            .field("program_id", .uuid, .required, .references("programs", "id", onDelete: .cascade))
            .field("period_number", .int, .required)
            .field("start_date", .date, .required)
            .field("end_date", .date, .required)
            .field("status", .string, .required)
            .field("outcome_status", .string)
            .field("outcome_description", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("review_periods").delete()
    }
}

struct CreateProgramTask: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("program_tasks")
            .id()
            .field("review_period_id", .uuid, .required, .references("review_periods", "id", onDelete: .cascade))
            .field("task_type", .string, .required)
            .field("title", .string, .required)
            .field("assessment_key", .string)
            .field("review_key", .string)
            .field("assigned_to", .string)
            .field("completed_at", .datetime)
            .field("due_date", .date, .required)
            .field("status", .string, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("program_tasks").delete()
    }
}

// MARK: - Remove ProgramId Migration
struct RemoveProgramIdField: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("programs")
            .deleteField("program_id")
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("programs")
            .field("program_id", .string, .required)
            .update()
    }
}

// MARK: - Request/Response DTOs

struct ProgramCreateRequest: Content {
    let programType: String
    let displayName: String
    let startDate: Date
    let endDate: Date
    let status: String
    let reviewFrequencyDays: Int
    let assignedBy: String
    let assignedTo: String
    let assignedToId: UUID?
    let reviewPeriods: [ReviewPeriodCreateRequest]
}

struct ReviewPeriodCreateRequest: Content {
    let periodNumber: Int
    let startDate: Date
    let endDate: Date
    let status: String
    let title: String?
    let outcomeStatus: String?
    let outcomeDescription: String?
    let assignedToId: UUID?
    let tasks: [ProgramTaskCreateRequest]
}

struct ProgramTaskCreateRequest: Content {
    let taskType: String
    let title: String
    let assessmentKey: String?
    let reviewKey: String?
    let assignedTo: String?
    let assignedToId: UUID?
    let completedAt: Date?
    let dueDate: Date
    let status: String
}

struct ProgramUpdateRequest: Content {
    let programType: String?
    let displayName: String?
    let startDate: Date?
    let endDate: Date?
    let status: String?
    let reviewFrequencyDays: Int?
    let assignedBy: String?
    let assignedTo: String?
    let assignedToId: UUID?
}

struct ReviewPeriodUpdateRequest: Content {
    let periodNumber: Int?
    let startDate: Date?
    let endDate: Date?
    let status: String?
    let title: String?
    let outcomeStatus: String?
    let outcomeDescription: String?
    let assignedToId: UUID?
}

struct ProgramTaskUpdateRequest: Content {
    let assignedTo: String?
    let assignedToId: UUID?
    let completedAt: Date?
    let status: String?
}

struct ProgramResponse: Content {
    let id: UUID?
    let programType: String
    let displayName: String
    let startDate: Date
    let endDate: Date
    let status: String
    let reviewFrequencyDays: Int
    let assignedBy: String
    let assignedTo: String
    let assignedToId: UUID?
    let reviewPeriods: [ReviewPeriodResponse]?
    let createdAt: Date?
    let updatedAt: Date?

    init(program: Program, reviewPeriods: [ReviewPeriodResponse]? = nil) {
        self.id = program.id
        self.programType = program.programType
        self.displayName = program.displayName
        self.startDate = program.startDate
        self.endDate = program.endDate
        self.status = program.status
        self.reviewFrequencyDays = program.reviewFrequencyDays
        self.assignedBy = program.assignedBy
        self.assignedTo = program.assignedTo
        self.assignedToId = program.$assignedToUser.id
        self.reviewPeriods = reviewPeriods
        self.createdAt = program.createdAt
        self.updatedAt = program.updatedAt
    }
}

struct ReviewPeriodResponse: Content {
    let id: UUID?
    let periodNumber: Int
    let startDate: Date
    let endDate: Date
    let status: String
    let title: String?
    let outcomeStatus: String?
    let outcomeDescription: String?
    let assignedToId: UUID?
    let tasks: [ProgramTaskResponse]?
    let createdAt: Date?
    let updatedAt: Date?

    init(reviewPeriod: ReviewPeriod, tasks: [ProgramTaskResponse]? = nil) {
        self.id = reviewPeriod.id
        self.periodNumber = reviewPeriod.periodNumber
        self.startDate = reviewPeriod.startDate
        self.endDate = reviewPeriod.endDate
        self.status = reviewPeriod.status
        self.title = reviewPeriod.title
        self.outcomeStatus = reviewPeriod.outcomeStatus
        self.outcomeDescription = reviewPeriod.outcomeDescription
        self.assignedToId = reviewPeriod.$assignedToUser.id
        self.tasks = tasks
        self.createdAt = reviewPeriod.createdAt
        self.updatedAt = reviewPeriod.updatedAt
    }
}

struct ProgramTaskResponse: Content {
    let id: UUID?
    let taskType: String
    let title: String
    let assessmentKey: String?
    let reviewKey: String?
    let assignedTo: String?
    let assignedToId: UUID?
    let completedAt: Date?
    let dueDate: Date
    let status: String
    let createdAt: Date?
    let updatedAt: Date?

    init(task: ProgramTask) {
        self.id = task.id
        self.taskType = task.taskType
        self.title = task.title
        self.assessmentKey = task.assessmentKey
        self.reviewKey = task.reviewKey
        self.assignedTo = task.assignedTo
        self.assignedToId = task.$assignedToUser.id
        self.completedAt = task.completedAt
        self.dueDate = task.dueDate
        self.status = task.status
        self.createdAt = task.createdAt
        self.updatedAt = task.updatedAt
    }
}
