//
//  AcuityScore.swift
//  hmbl-core
//
//  Created by Augment Agent on 9/25/25.
//

import Foundation
import Fluent
import Vapor

// MARK: - AcuityReference Structure
struct AcuityReference: Codable {
    let type: String        // "assessment", "note", "hospitalization", "careplan"
    let refId: UUID
    let title: String?      // Title of the linked entity
    let reason: String?     // Explanation of how it impacted the score
    
    enum CodingKeys: String, CodingKey {
        case type
        case refId = "ref_id"
        case title
        case reason
    }
}

// MARK: - AcuityScore Model
final class AcuityScore: Model, @unchecked Sendable {
    static let schema = "acuity_scores"
    
    @ID var id: UUID?
    @Parent(key: "member_id") var member: Member
    
    @Field(key: "score") var score: Int                    // Numerical value (e.g. 1-10)
    @Field(key: "level") var level: String                 // "Low", "Moderate", "High"
    @Field(key: "source") var source: String               // "Manual", "AssessmentEngine"
    @OptionalField(key: "notes") var notes: String?        // Human-readable notes
    @Field(key: "references") var references: [AcuityReference] // Supporting evidence
    
    @OptionalParent(key: "created_by_user_id") var createdByUser: User?
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
    
    init() { }
    
    init(id: UUID? = nil,
         score: Int,
         level: String,
         source: String,
         notes: String? = nil,
         references: [AcuityReference] = [],
         createdByUserId: UUID? = nil) {
        self.id = id
        self.score = score
        self.level = level
        self.source = source
        self.notes = notes
        self.references = references
        if let userId = createdByUserId {
            self.$createdByUser.id = userId
        }
    }
}

// MARK: - Content Conformance
extension AcuityScore: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case score
        case level
        case source
        case notes
        case references
        case createdByUser = "created_by_user"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Migration
struct CreateAcuityScore: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(AcuityScore.schema)
            .id()
            .field("member_id", .uuid, .required, .references(Member.schema, "id", onDelete: .cascade))
            .field("score", .int, .required)
            .field("level", .string, .required)
            .field("source", .string, .required)
            .field("notes", .string)
            .field("references", .json, .required)
            .field("created_by_user_id", .uuid, .references(User.schema, "id", onDelete: .setNull))
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }
    
    func revert(on database: Database) -> EventLoopFuture<Void> {
        return database.schema(AcuityScore.schema).delete()
    }
}
